
import { cacheManager } from './cache-manager'

/**
 * 缓存初始化器
 * 负责在应用启动时初始化缓存系统
 * 重构后的版本：确保主进程和渲染进程缓存数据同步
 */
export class CacheInitializer {

  private static initialized = false

  /**
   * 初始化缓存系统
   */
  public static async initialize(): Promise<void> {
    if (CacheInitializer.initialized) {
      return
    }

    try {
      console.log('[缓存系统] 开始初始化...')

      // 等待主进程资源缓存系统初始化完成
      await CacheInitializer.waitForMainProcessReady()

      // 初始化资源缓存管理器（从主进程同步数据）
      await cacheManager.resource.initializeCache()

      // 初始化字体缓存管理器
      // 字体缓存会在需要时懒加载，这里不需要预初始化

      CacheInitializer.initialized = true
      console.log('[缓存系统] 初始化完成')
    } catch (error) {
      console.error('[缓存系统] 初始化失败:', error)
      throw error
    }
  }

  /**
   * 等待主进程资源缓存系统准备就绪
   */
  private static async waitForMainProcessReady(): Promise<void> {
    const maxRetries = 10
    const retryDelay = 100 // 100ms

    for (let i = 0; i < maxRetries; i++) {
      try {
        // 尝试调用主进程的 getAllResources 方法
        await window.resource.getAllResources()
        console.log('[缓存系统] 主进程资源缓存系统已就绪')
        return
      } catch (error) {
        if (i === maxRetries - 1) {
          throw new Error(`主进程资源缓存系统未就绪: ${error}`)
        }
        console.log(`[缓存系统] 等待主进程就绪... (${i + 1}/${maxRetries})`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }
  }

  /**
   * 检查缓存系统是否已初始化
   */
  public static isInitialized(): boolean {
    return CacheInitializer.initialized
  }

  /**
   * 重置初始化状态（主要用于测试）
   */
  public static reset(): void {
    CacheInitializer.initialized = false
  }
}

/**
 * 自动初始化缓存系统
 * 在模块加载时自动调用
 */
CacheInitializer.initialize().catch(error => {
  console.error('[缓存系统] 自动初始化失败:', error)
})
