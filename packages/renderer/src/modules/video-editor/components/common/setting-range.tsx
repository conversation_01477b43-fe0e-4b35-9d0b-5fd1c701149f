import React, { <PERSON> } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'

export interface SettingRange {
  value: string
  onChange: (value: string) => void,
}

export const SettingRange: FC<SettingRange> = ({ value, onChange }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
        >
          {value}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="min-w-[100px] bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700"
        align="center"
      >
        <DropdownMenuItem
          onClick={() => onChange('1')}

        >
          应用全部
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
