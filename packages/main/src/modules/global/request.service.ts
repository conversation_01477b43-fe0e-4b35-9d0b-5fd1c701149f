import { Injectable, Logger } from '@nestjs/common'
import fetch from 'node-fetch'
import { ApiResponse } from '@app/shared/types/database.types.js'
import {
  AuthState,
  IPCRequestConfig,
  IPCRequestState,
  RequestStateManager,
  TeamState
} from '@app/shared/types/auth-request.types.js'

/**
 * 业务错误类
 */
export class BusinessError extends Error {

  constructor(
    public readonly code: number,
    message: string,
    public readonly originalData?: any
  ) {
    super(message)
    this.name = 'BusinessError'
  }
}

/**
 * 统一请求服务
 * 封装通用的 HTTP 请求逻辑，实现状态管理
 */
@Injectable()
export class RequestService implements RequestStateManager {

  private readonly logger = new Logger(RequestService.name)

  /**
   * 默认请求头
   */
  private readonly defaultHeaders = {
    'Content-Type': 'application/json',
  }

  /**
   * 默认超时时间（毫秒）
   */
  private readonly defaultTimeout = 10000

  /**
   * 统一请求状态
   */
  private requestState: IPCRequestState = {
    auth: {} as AuthState,
    team: {},
    baseUrl: process.env.API_BASE_URL || 'http://************:48080'
  }

  /**
   * 获取当前状态
   */
  getState(): IPCRequestState {
    return { ...this.requestState }
  }

  /**
   * 更新认证状态
   */
  updateAuthState(auth: Partial<AuthState>): void {
    this.requestState.auth = { ...this.requestState.auth, ...auth }
  }

  /**
   * 更新团队状态
   */
  updateTeamState(team: Partial<TeamState>): void {
    this.requestState.team = { ...this.requestState.team, ...team }
  }

  /**
   * 清除所有状态
   */
  clearState(): void {
    this.requestState = {
      auth: {} as AuthState,
      team: {},
      baseUrl: process.env.API_BASE_URL || 'http://************:48080'
    }
  }

  /**
   * 检查token是否过期
   */
  isTokenExpired(): boolean {
    const { expiresTime } = this.requestState.auth
    if (!expiresTime) return false
    return Date.now() >= expiresTime
  }

  /**
   * 批量更新请求状态
   */
  updateRequestState(state: Partial<IPCRequestState>): void {
    if (state.auth) {
      this.updateAuthState(state.auth)
    }
    if (state.team) {
      this.updateTeamState(state.team)
    }
    if (state.baseUrl) {
      this.requestState.baseUrl = state.baseUrl
    }
  }

  /**
   * GET 请求
   */
  async get<T = any>(url: string, config?: IPCRequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'GET',
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  async rawGet<T = any>(url: string, config?: IPCRequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'GET',
      headers: config?.headers,
      timeout: config?.timeout
    }, false)
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: IPCRequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'POST',
      body: data,
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * PUT 请求
   */
  async put<T = any>(url: string, data?: any, config?: IPCRequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'PUT',
      body: data,
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(url: string, config?: IPCRequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'DELETE',
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(url: string, data?: any, config?: IPCRequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'PATCH',
      body: data,
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * 通用请求方法
   */
  private async request<T>(
    url: string,
    options: {
      method: string
      body?: any
      headers?: Record<string, string>
      timeout?: number
    },
    processResponse = true
  ): Promise<T> {
    try {
      const fullUrl = this.buildUrl(url)
      const headers = this.mergeHeaders(options.headers)
      const timeout = options.timeout || this.defaultTimeout
      // this.logger.debug(`${options.method} ${fullUrl}`)

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(fullUrl, {
        method: options.method,
        headers,
        body: options.body ? JSON.stringify(options.body) : undefined,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text().catch(() => '无法获取错误详情')
        throw new Error(`HTTP ${response.status} ${response.statusText}: ${errorText}`)
      }

      if (processResponse) {
        return response.json().then(json => this.processResponse(json))
      } else {
        const text = await response.text()
        return text as T
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      this.logger.error(`${options.method} ${url} - 失败:`, errorMsg)
      throw error instanceof BusinessError ? error : new Error(errorMsg)
    }
  }

  /**
   * 合并请求头
   */
  private mergeHeaders(customHeaders?: Record<string, string>): Record<string, string> {
    const { auth, team } = this.requestState
    return {
      ...this.defaultHeaders,
      ...customHeaders,
      ...(team.tenantId && { 'tenant-id': String(team.tenantId) }),
      ...(auth.accessToken && { 'Authorization': `Bearer ${auth.accessToken}` }),
    }
  }

  /**
   * 构建完整URL
   */
  private buildUrl(url: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }

    const baseUrl = this.requestState.baseUrl

    if (!baseUrl) {
      throw new Error('Base URL not configured')
    }

    const cleanBaseUrl = baseUrl.replace(/\/$/, '')
    const cleanUrl = url.startsWith('/') ? url : `/${url}`

    return `${cleanBaseUrl}${cleanUrl}`
  }

  /**
   * 处理响应体
   */
  private processResponse<T = any>(data: any): T {
    if (data && typeof data === 'object' && 'code' in data) {
      const apiResponse = data as ApiResponse<T>

      if (apiResponse.code === 0 || apiResponse.code === 200) {
        return apiResponse.data as T
      }

      const errorMessage = apiResponse.msg || '未知业务错误'

      // 特殊状态码处理
      if (apiResponse.code === 401) {
        console.warn('[RequestService] 未授权，可能需要重新登录')
        // 在 main 进程中，可以通过 IPC 通知 renderer 进程处理登出逻辑
      }

      throw new BusinessError(apiResponse.code, errorMessage, apiResponse)
    }

    return data as T
  }
}
