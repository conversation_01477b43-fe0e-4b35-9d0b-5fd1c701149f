'use client'

import * as React from 'react'
import { type DateRange } from 'react-day-picker'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { zhCN } from 'date-fns/locale'
import { CaretSortIcon } from '@radix-ui/react-icons'
import { cn } from './lib/utils'
import { XCircle } from 'lucide-react'

export function DateRangePicker({
  onChange,
  value,
}: {
  onChange?: (range: DateRange | undefined) => void
  value?: DateRange | undefined
}) {
  const [open, setOpen] = React.useState(false)

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            id="dates"
            className="w-56 justify-between font-normal bg-transparent hover:bg-transparent"
            onClick={e => e.preventDefault()}
            onMouseDown={() => setOpen(v => !v)}
          >
            {value?.from && value?.to ? (
              `${value.from.toLocaleDateString()} - ${value.to.toLocaleDateString()}`
            ) : (
              <span className="text-muted-foreground">选择日期范围</span>
            )}
            <CaretSortIcon className="h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto overflow-hidden p-0" align="start">
          <Calendar
            locale={zhCN}
            mode="range"
            selected={value}
            captionLayout="dropdown"
            onSelect={onChange}
          />
        </PopoverContent>
      </Popover>
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          'absolute top-1/2 -translate-y-1/2 right-[11px] z-10 size-6 hidden',
          value && 'flex',
        )}
        onClick={e => {
          e.preventDefault()
          e.stopPropagation()
          onChange?.(undefined)
        }}
      >
        <XCircle className="size-4.5 fill-gray-300 text-background" />
      </Button>
    </div>
  )
}
