import { Injectable, Inject } from '@nestjs/common'
import { FileUploaderService } from './file-uploader.service.js'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'

@Injectable()
export class FileUploaderIPCHandler extends BaseIPCHandler<'fileUploader'> {

  protected readonly platformPrefix = 'fileUploader'

  constructor(
    @Inject(FileUploaderService) private fileUploaderService: FileUploaderService
  ) {
    super()
  }

  /**
   * 注册所有 IPC 处理程序
   */
  registerAll(): void {
    this.registerHandler('uploadBufferToOSS', async data => {
      return this.fileUploaderService.uploadBufferToOSS(data)
    })
  }
}
