import { DndContext, DragMoveEvent, DragStartEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import React, { PropsWithChildren, useCallback } from 'react'
import { useTimelineDnd } from '@/modules/video-editor/contexts'
import { useTimelineItemResizing } from '@/modules/video-editor/hooks/draggable-handlers/useTimelineItemResizing'

export const TimelineItemHandleDndWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  const { handleResizeMove } = useTimelineItemResizing()
  const { handleTimelineItemAdjustStart, handleTimelineItemAdjustEnd } = useTimelineDnd()

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 4,
      }
    })
  )

  const onDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event

    if (active.data.current?.type !== 'timeline-item-handle') return

    const { overlay } = active.data.current as any
    return handleTimelineItemAdjustStart(overlay)
  }, [handleTimelineItemAdjustStart])

  const onDragMove = useCallback((event: DragMoveEvent) => {
    const { x: deltaX } = event.delta
    return handleResizeMove(deltaX)
  }, [handleResizeMove])

  return (
    <DndContext sensors={sensors} onDragStart={onDragStart} onDragMove={onDragMove} onDragEnd={handleTimelineItemAdjustEnd}>
      {children}
    </DndContext>
  )
}
