import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'
import dayjs from 'dayjs'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 格式化时间戳为可读时间
 * @param timestamp 时间戳（毫秒）
 * @param format 时间格式，默认为 'YYYY-MM-DD HH:mm'
 * @returns 格式化后的时间字符串，如果时间戳无效则返回 '-'
 */
export function formatTimestamp(timestamp?: number, format: string = 'YYYY-MM-DD HH:mm'): string {
  if (!timestamp) return '-'
  return dayjs(timestamp).format(format)
}
