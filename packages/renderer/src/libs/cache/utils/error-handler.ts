
export function isIPCError(error: any): boolean {
  return error
    && typeof error === 'object'
    && 'message' in error
    && typeof error.message === 'string'
    && error.message.includes('Error invoking remote method')
}

/**
 * 从调用 Electron API 的错误中提取错误信息
 */
export function extractMessageFromIPCError(error: any): any {
  if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
    return error.message.replace(/Error invoking remote method '\w+:\w+': (Error: )?/, '')
  }

  return '未知 IPC 错误'
}
