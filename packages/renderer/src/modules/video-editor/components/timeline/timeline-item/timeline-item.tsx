import React, { FC, Fragment, memo, useCallback, useMemo, useRef } from 'react'
import { CaptionOverlay, Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { VideoOverlayKeyframe } from './video-overlay-keyframe'
import { TimelineItemHandle } from './timeline-item-handle'
import { TimelineItemContextMenu } from './timeline-item-context-menu'
import { StyleOnlyTimelineItem } from './style-only-timeline-item'
import TimelineCaptionBlocks from './timeline-caption-blocks'
import clsx from 'clsx'
import { useEditorContext, useTimeline, useTimelineDnd } from '@/modules/video-editor/contexts'
import { findOverlayStoryboard } from '@/modules/video-editor/utils/overlay-helper'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { Volume2, VolumeOff } from 'lucide-react'
import { clamp } from 'lodash'
import { TimelineItemHandleDndWrapper } from './timeline-item-handle-dnd-wrapper'
import { useTimelineTrackContext } from '../timeline-track/timeline-track-context'
import { EditorDraggableTypes, useTypedDraggable } from '../../editor-dnd-wrapper'

export interface TimelineItemProps {
  item: Overlay
}

const useAdjustedStyles = (overlay: Overlay) => {
  const { zoomScale } = useTimeline()
  const { currentTrack } = useTimelineTrackContext()
  const { previewOverlaysAdjust, draggingOverlay  } = useTimelineDnd()
  const { tracks, durationInFrames: totalDuration } = useEditorContext()

  const isCurrentOverlayDragging = useMemo(
    () => draggingOverlay?.id === overlay.id,
    [draggingOverlay, overlay]
  )

  const left = useMemo(() => {
    const adjust = isCurrentOverlayDragging
      ? 0
      : previewOverlaysAdjust.get(overlay.id)?.fromFrameShift ?? 0

    return (overlay.from + adjust) * zoomScale * PIXELS_PER_FRAME
  }, [overlay, totalDuration, isCurrentOverlayDragging, zoomScale, previewOverlaysAdjust])

  const width = useMemo(() => {
    const adjustOfCurrent = previewOverlaysAdjust.get(overlay.id)?.durationShift ?? 0
    const adjustedDuration = overlay.durationInFrames + adjustOfCurrent

    const storyboard = findOverlayStoryboard(tracks, overlay)
    // 分镜本身不需要限制长度
    if (!storyboard || overlay.type === OverlayType.STORYBOARD || currentTrack.isGlobalTrack) {
      return (overlay.durationInFrames + adjustOfCurrent) * zoomScale * PIXELS_PER_FRAME
    }

    const adjustOfStoryboard = previewOverlaysAdjust.get(storyboard.id)?.durationShift ?? 0
    const storyboardEndFrame = storyboard.from + storyboard.durationInFrames + adjustOfStoryboard

    const limitedDuration = clamp(adjustedDuration, 0, storyboardEndFrame - overlay.from)
    return limitedDuration * zoomScale * PIXELS_PER_FRAME
  }, [currentTrack, overlay, tracks, zoomScale, totalDuration, previewOverlaysAdjust])

  return {
    left,
    width
  }
}

const TimelineItemContent: FC<Overlay> = item => {
  const {
    durationInFrames,
    videoPlayer: { currentFrame }
  } = useEditorContext()

  // const waveformData = useWaveformProcessor(
  //   item.type === OverlayType.SOUND ? item.src : undefined,
  //   item.type === OverlayType.SOUND ? item.startFromSound : undefined,
  //   item.durationInFrames,
  // )

  return (
    <>
      {item.type === OverlayType.CAPTION && (
        <div className="relative h-full">
          <TimelineCaptionBlocks
            captions={(item as CaptionOverlay).captions}
            durationInFrames={item.durationInFrames}
            currentFrame={currentFrame ?? 0}
            startFrame={item.from}
            totalDuration={durationInFrames}
          />
        </div>
      )}

      {/* {item.type === OverlayType.SOUND && waveformData && (
        <div className="absolute inset-0">
          <WaveformVisualizer
            waveformData={waveformData}
            totalDuration={durationInFrames}
            durationInFrames={item.durationInFrames}
          />
        </div>
      )} */}

      {item.type === OverlayType.VIDEO && (
        <Fragment>
          <VideoOverlayKeyframe overlay={item} />

          <div className="absolute inset-0 group-hover:opacity-100 opacity-0 transition-all duration-150">
            <div className="flex h-full justify-end items-end pr-4 pb-0.5">
              <div
                className="bg-gray-500/80 size-4 flex justify-center items-center"
                style={{ zIndex: 99 }}
                // onClick={e => {
                //   e.stopPropagation()
                //   setCurrentOverlayVolume(item.styles.volume ? 0 : 1)
                // }}
              >
                {
                  typeof item.styles.volume === 'number' && item.styles.volume > 0
                    ? <Volume2 className="size-3 text-white" />
                    : <VolumeOff className="size-3 text-white" />
                }
              </div>
            </div>
          </div>
        </Fragment>
      )}
    </>
  )
}

const TimelineItem: React.FC<TimelineItemProps> = ({ item }) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(
    EditorDraggableTypes.TimelineItem,
    item.id,
    {
      overlay: item,
    }
  )

  const { isDragging, draggingOverlay } = useTimelineDnd()
  const { toggleOverlaySelection, getOverlayActivationState } = useTimeline()

  const {
    selectedOverlay,
    setSelectedOverlay,
    videoPlayer: { seekTo }
  } = useEditorContext()

  const adjustedStyles = useAdjustedStyles(item)

  const itemRef = useRef<HTMLDivElement>(null)

  const isSelected = useMemo(
    () => selectedOverlay?.id === item.id,
    [selectedOverlay]
  )

  const isCurrentOverlayDragging = useMemo(
    () => draggingOverlay?.id === item.id,
    [draggingOverlay, item]
  )

  const selectCurrentOverlay = useCallback(() => {
    const target = item
    setSelectedOverlay(item)
    seekTo(target.from)
    toggleOverlaySelection(target)
  }, [setSelectedOverlay, toggleOverlaySelection])

  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation()
    selectCurrentOverlay()
  }

  const containerClassName = 'absolute inset-y-px select-none pointer-events-auto overflow-hidden'

  return (
    <TimelineItemContextMenu overlay={item}>
      <div
        ref={ref => {
          itemRef.current = ref
          setNodeRef(ref)
        }}
        className={clsx(
          isDragging && isCurrentOverlayDragging && 'opacity-0',
          containerClassName,
          !getOverlayActivationState(item) && 'grayscale',
          isSelected
            ? 'border-2 border-black dark:border-white rounded-md'
            : 'border-0',
        )}
        style={{
          left: adjustedStyles.left,
          width: adjustedStyles.width,
          zIndex: isDragging ? 1 : isSelected ? 35 : 30, // 选中时增加 z-index
          transition: 'opacity 0.2s, left 0.15s ease-out'
        }}
        {...listeners}
        {...attributes}
        onClick={e => {
          e.stopPropagation() // 防止事件冒泡到时间轴点击处理器
          handleSelect(e)
        }}
      >
        <StyleOnlyTimelineItem
          item={item}
          width={adjustedStyles.width}
          className="w-full h-full relative"
        >
          <TimelineItemHandleDndWrapper>
            <TimelineItemContent {...item} />

            {/* TODO: 暂时排除视频和音频的拖拽, 后续可能需要配合变速功能 */}
            {/*{![OverlayType.VIDEO, OverlayType.SOUND].includes(item.type) && (*/}
            <TimelineItemHandle
              disabled={isDragging && !isCurrentOverlayDragging }
              position="right"
              isSelected={isSelected}
              overlay={item}
            />
            {/*)}*/}
          </TimelineItemHandleDndWrapper>
        </StyleOnlyTimelineItem>
      </div>
    </TimelineItemContextMenu>
  )
}

const MemoizedTimelineItem = memo(TimelineItem)

export {
  MemoizedTimelineItem as TimelineItem
}
