/**
 * 更新检查结果
 */
export interface UpdateCheckResult {
  /**
   * 是否有可用更新
   */
  hasUpdate: boolean
  /**
   * 新版本号（仅在有更新时返回）
   */
  version?: string
}

/**
 * 更新启动结果
 */
export interface UpdateStartResult {
  /**
   * 是否成功启动更新
   */
  started: boolean
}

/**
 * 更新下载进度信息
 */
export interface UpdateDownloadProgress {
  /**
   * 下载进度百分比 (0-100)
   */
  progress: number
  /**
   * 下载速度 (字节/秒)
   */
  bytesPerSecond: number
  /**
   * 下载进度百分比 (精确值)
   */
  percent: number
  /**
   * 总大小 (字节)
   */
  total: number
  /**
   * 已下载大小 (字节)
   */
  transferred: number
}

/**
 * 更新下载完成信息
 */
export interface UpdateDownloaded {
  /**
   * 新版本号
   */
  version: string
}

/**
 * 更新错误信息
 */
export interface UpdateError {
  /**
   * 错误消息
   */
  error: string
}

/**
 * 自动更新器IPC客户端接口
 */
export interface AutoUpdaterIPCClient {
  /**
   * 检查更新
   * @returns 更新检查结果
   */
  checkForUpdates(): Promise<UpdateCheckResult>

  /**
   * 开始更新
   * @returns 更新启动结果
   */
  startUpdate(): Promise<UpdateStartResult>

  /**
   * 安装更新并重启应用
   */
  installUpdate(): Promise<void>
}
