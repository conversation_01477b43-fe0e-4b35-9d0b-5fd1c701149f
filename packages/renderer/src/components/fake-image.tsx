import React from 'react'
import { cn } from './lib/utils'

type Props = {
  id: number
  className?: string
  style?: React.CSSProperties
  type?: 'two' | 'three'
}

function mulberry32(a: number) {
  return function () {
    let t = (a += 0x6d2b79f5)
    t = Math.imul(t ^ (t >>> 15), t | 1)
    t ^= t + Math.imul(t ^ (t >>> 7), t | 61)
    return ((t ^ (t >>> 14)) >>> 0) / 4294967296
  }
}

const cache: Record<number, string> = {}
const cache3: Record<number, string> = {}

export const randomGradient = (id: number) => {
  if (cache[id]) return cache[id]
  const rng = mulberry32(id)
  const h1 = rng() * 360
  const h2 = rng() * 360
  const deg = rng() * 360
  const gradient = `linear-gradient(${deg}deg, hsl(${h1}, 70%, 60%), hsl(${h2}, 75%, 65%))`
  cache[id] = gradient
  return gradient
}

export const randomGradient3 = (id: number) => {
  if (cache3[id]) return cache3[id]
  const rng = mulberry32(id)
  const rgb = (index: number, opacity = 0) => {
    const arr = Array(3).fill(0)
    arr[index] = 255
    return `rgb(${arr.join(' ')} / ${opacity})`
  }
  return Array(3)
    .fill(0)
    .map((_, i) => `linear-gradient(${rng()}turn, ${rgb(i, 1)}, ${rgb(i, 0)} 70.71%)`)
    .join(', ')
}

const randomMapping: Record<NonNullable<Props['type']>, (id: number) => string> = {
  two: randomGradient,
  three: randomGradient3,
}

export const FakeImage: React.FC<Props> = ({ id, className = '', style, type = 'two' }) => {
  const gradient = randomMapping[type](id)

  return (
    <div
      className={cn(
        'w-full h-full after:content-[attr(div)] after:bg-black after:absolute after:inset-0 after:opacity-0 dark:after:opacity-25',
        className,
      )}
      style={{
        backgroundImage: gradient,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        ...style,
      }}
    />
  )
}
