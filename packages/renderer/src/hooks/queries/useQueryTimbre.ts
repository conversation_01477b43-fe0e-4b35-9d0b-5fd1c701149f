import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'
import { TimbreResource } from '@/types/resources'
import { useInfiniteQuery } from '../useInfiniteQuery'
import { PaginationParams } from '@app/shared/types/database.types'

/**
 * 使用无限查询获取音色列表，支持无限滚动加载
 * @param params 查询参数
 * @returns 无限查询结果
 */
export const useInfiniteQueryTimbreList = (params: PaginationParams = {}) => {
  return useInfiniteQuery<TimbreResource.Timbre>(
    [QUERY_KEYS.TIMBRE_LIST],
    ResourceModule.timbre.list,
    params,
    {
      pageSize: params.pageSize || 12,
    }
  )
}
