import React, { KeyboardEvent, ReactNode, useState } from 'react'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { CategorySelector } from '../../components/common/category-selector'
import { CommonCategory } from '@/types/resources'
import { Input } from '@/components/ui/input'
import { DurationFilter } from '../../components/common/duration-filter'

export type ResourceTab = {
  label: string
  value: string
  /**
   * 是否显示搜索框
   */
  showSearch?: boolean
  /**
   * 是否显示分类选择器
   */
  showCategorySelector?: boolean
  /**
   * 是否显示时长筛选器
   */
  showDurationFilter?: boolean
  /**
   * 搜索框占位符文本
   */
  searchPlaceholder?: string
  /**
   * 标签页内容渲染函数
   */
  renderContent?: () => ReactNode
  /**
   * 资源列表是否为空
   */
  isEmpty?: boolean
  /**
   * 空状态提示文本
   */
  emptyText?: string
}

export interface ResourcePanelLayoutProps {
  /**
   * 标签页配置
   */
  tabs: ResourceTab[]
  /**
   * 默认选中的标签页
   */
  defaultTab: string
  /**
   * 分类数据
   */
  categories?: CommonCategory[]
  /**
   * 当前选中的分类ID
   */
  selectedCategory?: string
  /**
   * 分类变更回调
   */
  onCategoryChange?: (value: string) => void
  /**
   * 搜索关键字
   */
  searchKey?: string
  /**
   * 搜索关键字变更回调
   */
  onSearchChange?: (value: string) => void
  /**
   * 标签页切换回调
   */
  onTabChange?: (value: string) => void
  /**
   * 当前选中的时长范围
   */
  selectedDuration?: string
  /**
   * 时长范围变更回调
   */
  onDurationChange?: (duration: string) => void
}

/**
 * 渲染空状态提示
 */
export function EmptyResourceState({ text = '暂无资源' }: { text?: string }) {
  return (
    <div className="flex justify-center items-center h-40 text-gray-400">
      {text}
    </div>
  )
}

/**
 * 资源内容包装器
 * 处理空状态显示
 */
export function ResourceContent({ isEmpty, emptyText, children }: { isEmpty?: boolean, emptyText?: string, children: ReactNode }) {
  if (isEmpty) {
    return <EmptyResourceState text={emptyText} />
  }

  return <>{children}</>
}

/**
 * 资源面板布局组件
 * 提供灵活的标签页布局，支持搜索和分类选择器
 */
export function ResourcePanelLayout({
  tabs,
  defaultTab,
  categories = [],
  selectedCategory = undefined,
  searchKey,
  onSearchChange,
  onCategoryChange,
  onTabChange,
  selectedDuration,
  onDurationChange,
}: ResourcePanelLayoutProps) {
  const [searchInputs, setSearchInputs] = useState<Record<string, string>>({})

  // 初始化搜索输入框的值
  React.useEffect(() => {
    if (searchKey !== undefined) {
      setSearchInputs(prev => {
        const newInputs = { ...prev }
        tabs.forEach(tab => {
          if (newInputs[tab.value] === undefined) {
            newInputs[tab.value] = searchKey
          }
        })
        return newInputs
      })
    }
  }, [searchKey, tabs])

  const handleSearchInputChange = React.useCallback((tabValue: string, value: string) => {
    setSearchInputs(prev => ({
      ...prev,
      [tabValue]: value
    }))
  }, [])

  const handleKeyDown = (tabValue: string) => (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const inputValue = searchInputs[tabValue] || ''
      onSearchChange?.(inputValue)
    }
  }

  return (
    <div className="flex flex-col h-full w-full overflow-hidden">
      <div className="flex-none p-4 pb-0 h-full">
        <Tabs defaultValue={defaultTab} onValueChange={onTabChange} className="w-full h-full flex flex-col">
          <TabsList className="w-full flex-none flex space-x-1 bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-1">
            {tabs.map(tab => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="flex-1 px-3 py-1.5 text-sm font-medium
                  data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800
                  data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400
                  data-[state=active]:shadow-sm
                  rounded-md transition-all duration-200
                  text-gray-600 dark:text-gray-400"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          {tabs.map(tab => (
            <TabsContent key={tab.value} value={tab.value} className="mt-2 flex flex-col flex-grow overflow-hidden">
              {/* 搜索框、分类选择器和时长筛选器 */}
              {(tab.showSearch !== false || tab.showCategorySelector !== false || tab.showDurationFilter) && (
                <div className="flex-col flex px-4 gap-3 mt-2">
                  {/* 搜索框 */}
                  {tab.showSearch !== false && (
                    <div>
                      <Input
                        placeholder={tab.searchPlaceholder || '搜索资源，按回车键搜索'}
                        value={searchInputs[tab.value] || ''}
                        onChange={e => handleSearchInputChange(tab.value, e.target.value)}
                        onKeyDown={handleKeyDown(tab.value)}
                        className="w-full rounded border-gray-700 focus-visible:ring-gray-600"
                      />
                    </div>
                  )}
                  {/* 分类选择器 */}
                  {tab.showCategorySelector !== false && categories.length > 0 && (
                    <CategorySelector
                      options={categories}
                      value={selectedCategory}
                      onValueChange={onCategoryChange}
                      className="mb-2"
                    />
                  )}
                  {/* 时长筛选器 */}
                  {tab.showDurationFilter && (
                    <DurationFilter
                      selectedDuration={selectedDuration}
                      onDurationChange={onDurationChange}
                    />
                  )}
                </div>
              )}

              {/* 标签页内容 */}
              <div className="flex-grow overflow-y-auto px-4 pb-4">
                <ResourceContent isEmpty={tab.isEmpty} emptyText={tab.emptyText}>
                  {tab.renderContent?.()}
                </ResourceContent>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}

export default ResourcePanelLayout
