import { useCallback, useState } from 'react'
import { AspectRatio, PlayerDimensions } from '../../types'

export type AspectRatioHook = {
  /**
   * 当前的宽高比
   */
  aspectRatio: AspectRatio

  /**
   * 更新宽高比的方法
   */
  setAspectRatio(ratio: AspectRatio): void

  /**
   * 播放器的当前尺寸
   */
  playerDimensions: { width: number, height: number }

  /**
   * 更新播放器尺寸的方法
   * @param width
   * @param height
   */
  updatePlayerDimensions(width: number, height: number): void

  /**
   * 根据宽高比, 计算播放器尺寸
   */
  getPlayerDimensions(): PlayerDimensions
}

/**
 * Custom hook for managing aspect ratio and player dimensions.
 * @param initialRatio - The initial aspect ratio to use (default: "16:9")
 * @param onRatioChange - Callback function to call when the aspect ratio changes (optional)
 * @returns An object containing aspect ratio state and related functions
 */
export const useAspectRatio = (
  initialRatio: AspectRatio = '3:4',
  onRatioChange?: (ratio: AspectRatio) => void,
): AspectRatioHook => {
  // Single source of truth for aspect ratio
  const [aspectRatio, setAspectRatio] = useState<AspectRatio>(initialRatio)

  const handleAspectRatioChange = useCallback(
    (newRatio: AspectRatio) => {
      setAspectRatio(newRatio)
      onRatioChange?.(newRatio)
    },
    [onRatioChange],
  )

  const [playerDimensions, setPlayerDimensions] = useState({
    width: 640,
    height: 360,
  }) // Default 16:9 dimensions

  /**
   * Updates the player dimensions based on the container size and current aspect ratio.
   * @param containerWidth - The width of the container
   * @param containerHeight - The height of the container
   */
  const updatePlayerDimensions = useCallback(
    (containerWidth: number, containerHeight: number) => {
      let width, height

      // Calculate target aspect ratio
      const [, hSide, vSide] = /^(\d*):(\d*)$/.exec(aspectRatio) || [4, 3]
      const targetRatio = Number(hSide) / Number(vSide)

      // Compare container ratio with target ratio to determine fitting strategy
      const containerRatio = containerWidth / containerHeight

      if (containerRatio > targetRatio) {
        // Container is wider than target ratio - fit to height
        height = containerHeight
        width = height * targetRatio
      }
      else {
        // Container is taller than target ratio - fit to width
        width = containerWidth
        height = width / targetRatio
      }

      setPlayerDimensions({ width, height })
    },
    [aspectRatio],
  )

  const getPlayerDimensions = useCallback(() => {
    switch (aspectRatio) {
      case '9:16':
        return { playerWidth: 1080, playerHeight: 1920 } // TikTok/Story
      case '4:5':
        return { playerWidth: 1080, playerHeight: 1350 } // Instagram Post
      case '1:1':
        return { playerWidth: 1080, playerHeight: 1080 } // Square Post
      case '16:9':
        return { playerWidth: 1920, playerHeight: 1080 }
      case '4:3':
        return { playerWidth: 1920, playerHeight: 1440 }
      case '3:4':
        return { playerWidth: 1440, playerHeight: 1920 }
      default:
        return { playerWidth: 1920, playerHeight: 1080 } // Laptop (16:9)
    }
  }, [aspectRatio])

  return {
    aspectRatio,
    setAspectRatio: handleAspectRatioChange,
    playerDimensions,
    updatePlayerDimensions,
    getPlayerDimensions,
  }
}
