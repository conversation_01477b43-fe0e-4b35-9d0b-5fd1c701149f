
/**
 * Gets the dimensions of an image from URL
 */
export const getImageDimensions = async (
  imageUrl: string,
): Promise<{ width: number; height: number } | null> => {
  return new Promise(resolve => {
    const img = new Image()

    // Set timeout to handle cases where image loading hangs
    const timeoutId = setTimeout(() => {
      console.warn('Image dimension detection timed out')
      resolve(null)
    }, 5000) // 5 second timeout

    img.onload = () => {
      clearTimeout(timeoutId)
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }

    img.onerror = () => {
      clearTimeout(timeoutId)
      console.error('Error loading image for dimension detection')
      resolve(null)
    }

    img.src = imageUrl
  })
}

