# FileUploader 组件使用指南

## 概述

FileUploader 是一个功能强大且高度可定制的文件上传组件，支持拖拽上传、自定义样式渲染、loading状态管理等功能。经过优化后，组件具有更高的样式自由度和更好的可维护性。

## 主要特性

- ✅ 拖拽上传支持
- ✅ 多文件上传
- ✅ 文件类型验证
- ✅ 文件大小限制
- ✅ **自定义渲染组件** (新增)
- ✅ **Loading 状态管理** (优化)
- ✅ 错误处理
- ✅ 预览功能（图片）
- ✅ **高复用性和可读性** (优化)

## 基本用法

### 默认样式

```tsx
import { FileUploader } from './file-uploader'

<FileUploader 
  fileTypes={['image/*', 'video/*']}
  maxFiles={5}
  maxSize={100 * 1024 * 1024} // 100MB
  onUpload={(files) => console.log('上传完成:', files)}
  onError={(error) => console.error('上传错误:', error)}
  folderUuId={'xxx'}
/>
```

### 自定义渲染组件

```tsx
import { FileUploader, FileUploaderRenderProps } from './file-uploader'

const CustomUploader: React.FC<FileUploaderRenderProps> = ({
  isDragActive,
  getRootProps,
  getInputProps,
  uploadedFiles,
  removeFile,
  disabled,
  isLoading,
  fileTypes,
  maxSize,
}) => {
  return (
    <div>
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {isLoading ? '上传中...' : '点击上传'}
      </div>
      {/* 自定义文件列表渲染 */}
    </div>
  )
}

<FileUploader renderCustomComponent={CustomUploader} />
```

## API 参考

### FileUploaderProps

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `fileTypes` | `string[]` | `['video/*', 'image/*']` | 允许的文件类型 |
| `maxFiles` | `number` | `5` | 最大文件数量 |
| `maxSize` | `number` | `100 * 1024 * 1024` | 最大文件大小（字节） |
| `multiple` | `boolean` | `true` | 是否允许多文件上传 |
| `disabled` | `boolean` | `false` | 是否禁用组件 |
| `className` | `string` | - | 自定义CSS类名 |
| `value` | `UploadedFile[]` | `[]` | 受控模式的文件列表 |
| `onChange` | `(files: UploadedFile[]) => void` | - | 文件列表变化回调 |
| `onUpload` | `(files: UploadedFile[]) => void` | - | 上传完成回调 |
| `onError` | `(error: string) => void` | - | 错误处理回调 |
| `renderCustomComponent` | `(props: FileUploaderRenderProps) => React.ReactNode` | - | **自定义渲染组件** |

### UploadedFile

```tsx
interface UploadedFile {
  id: string
  file: File
  preview?: string        // 图片预览URL
  status: 'uploading' | 'success' | 'error'
  error?: string         // 错误信息
  url?: string          // 上传成功后的文件URL
  fileName?: string     // 服务器返回的文件名
}
```

### FileUploaderRenderProps

```tsx
interface FileUploaderRenderProps {
  isDragActive: boolean                    // 是否正在拖拽
  getRootProps: () => any                 // 拖拽区域属性
  getInputProps: () => any                // 文件输入属性
  uploadedFiles: UploadedFile[]           // 文件列表
  removeFile: (fileId: string) => void    // 删除文件方法
  disabled: boolean                       // 是否禁用
  isLoading: boolean                      // **是否正在上传** (新增)
  fileTypes: string[]                     // 允许的文件类型
  maxSize: number                         // 最大文件大小
}
```

## 优化亮点

### 1. 自定义样式渲染
- 通过 `renderCustomComponent` 属性完全自定义组件外观
- 提供完整的渲染属性，包括状态和方法
- 保持功能完整性的同时提供最大的样式自由度

### 2. Loading 状态管理
- 移除复杂的 progress 进度条
- 使用简洁的 `isLoading` 布尔状态
- 自动计算全局 loading 状态
- 在上传区域和文件列表中都有 loading 指示

### 3. 代码优化
- 使用 `useMemo` 和 `useCallback` 优化性能
- 移除冗余的 progress 相关代码
- 提高代码可读性和可维护性
- 更好的类型安全

### 4. 高复用性
- 核心逻辑与 UI 分离
- 易于扩展和定制
- 保持向后兼容性


## 注意事项

1. **移除了 progress 属性**：不再支持详细的上传进度，使用 `isLoading` 状态代替
2. **自定义组件优先级**：如果提供了 `renderCustomComponent`，将完全替代默认UI
3. **文件验证**：组件会自动验证文件类型和大小

