/**
 * 简单的事件总线 Hook
 * 用于组件间通信，特别是跨组件的事件传递
 */

import { useCallback, useEffect, useRef } from 'react'

type EventCallback = (...args: any[]) => void
type EventMap = Record<string, EventCallback[]>

// 全局事件总线实例
class EventBus {

  private events: EventMap = {}

  // 订阅事件
  on(eventName: string, callback: EventCallback) {
    if (!this.events[eventName]) {
      this.events[eventName] = []
    }
    this.events[eventName].push(callback)

    // 返回取消订阅函数
    return () => {
      this.off(eventName, callback)
    }
  }

  // 取消订阅
  off(eventName: string, callback: EventCallback) {
    if (!this.events[eventName]) return

    const index = this.events[eventName].indexOf(callback)
    if (index > -1) {
      this.events[eventName].splice(index, 1)
    }
  }

  // 发布事件
  emit(eventName: string, ...args: any[]) {
    if (!this.events[eventName]) return

    this.events[eventName].forEach(callback => {
      try {
        callback(...args)
      } catch (error) {
        console.error(`事件处理器执行失败 [${eventName}]:`, error)
      }
    })
  }

  // 清除所有事件监听器
  clear() {
    this.events = {}
  }
}

// 全局事件总线实例
const globalEventBus = new EventBus()

/**
 * 事件发布 Hook
 */
export const useEventEmitter = () => {
  return useCallback((eventName: string, ...args: any[]) => {
    globalEventBus.emit(eventName, ...args)
  }, [])
}

/**
 * 事件订阅 Hook
 */
export const useEventListener = (eventName: string, callback: EventCallback) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    const wrappedCallback = (...args: any[]) => {
      callbackRef.current(...args)
    }

    return globalEventBus.on(eventName, wrappedCallback)
  }, [eventName])
}

// 事件名称常量
export const EVENT_NAMES = {
  HIGHLIGHT_SCRIPT_BUTTONS: 'highlight-script-buttons',
} as const

export type EventNames = typeof EVENT_NAMES[keyof typeof EVENT_NAMES]
