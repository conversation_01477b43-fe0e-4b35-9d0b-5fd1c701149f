import { Overlay, OverlayType, TextOverlay } from '@clipnest/remotion-shared/types'
import React, { useCallback, useMemo } from 'react'
import clsx from 'clsx'
import { StyleOnlyTimelineItem, TimelineItem } from '../timeline-item'
import { TrackGapIndicator } from './track-gap-indicator'
import { findLastOverlay, getOverlayTimeRange } from '@/modules/video-editor/utils/overlay-helper'
import { DEFAULT_TEXT_OVERLAY_STYLES, PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { getTrackTypeLabel, isOverlayAcceptableByTrack } from '@/modules/video-editor/utils/track-helper'
import { useOverlayHelper } from '@/modules/video-editor/hooks/helpers/useOverlayHelper'
import { GhostElement, Track, TrackType } from '../../../types'
import { useEditorContext, useTimeline, useTimelineDnd } from '@/modules/video-editor/contexts'
import { TimelineTrackContextMenu } from './timeline-track-context-menu'
import { TimelineTrackContext } from './timeline-track-context'
import { EditorDroppableTypes, useTypedDroppable } from '../../editor-dnd-wrapper'

export interface TimelineTrackProps extends Track {
  trackIndex: number
}

/**
 * Finds gaps between overlay items in a single timeline row
 * @param rowItems - Array of Overlay items in the current row
 * @returns Array of gap objects, each containing start and end times
 *
 * @example
 * // For a row with items: [0-30], [50-80], [100-120]
 * // Returns: [{start: 30, end: 50}, {start: 80, end: 100}]
 *
 * @description
 * This function identifies empty spaces (gaps) between overlay items in a timeline row:
 * 1. Converts each item into start and end time points
 * 2. Sorts all time points chronologically
 * 3. Identifies three types of gaps:
 *    - Gaps at the start (if first item doesn't start at 0)
 *    - Gaps between items
 *    - Gaps at the end are not considered as they're considered infinite
 */
function findGapsInRow(rowItems: Overlay[]) {
  if (rowItems.length === 0) return []

  const timePoints = rowItems
    .flatMap(item => [
      { time: item.from, type: 'start' },
      { time: item.from + item.durationInFrames, type: 'end' },
    ])
    .sort((a, b) => a.time - b.time)

  // Handle special case: if no items start at 0, add a gap from 0
  const gaps: { start: number, end: number }[] = []

  // Handle gap at the start
  if (timePoints.length > 0 && timePoints[0].time > 0) {
    gaps.push({ start: 0, end: timePoints[0].time })
  }

  // Handle gaps between items
  for (let i = 0; i < timePoints.length - 1; i++) {
    const currentPoint = timePoints[i]
    const nextPoint = timePoints[i + 1]

    if (
      currentPoint.type === 'end'
      && nextPoint.type === 'start'
      && nextPoint.time > currentPoint.time
    ) {
      gaps.push({ start: currentPoint.time, end: nextPoint.time })
    }
  }

  return gaps
}

const TrailingContainer: React.FC<TimelineTrackProps> = ({
  type, overlays, trackIndex, isGlobalTrack
}) => {
  const { zoomScale } = useTimeline()
  const { isDragging } = useTimelineDnd()
  const { appendOverlayToTrack } = useOverlayHelper()
  const { durationInFrames, playerDimensions, getPlayerDimensions } = useEditorContext()

  // 计算添加按钮的位置
  const styles = useMemo(
    () => {
      if (overlays.length === 0) {
        // 空轨道时显示在起始位置
        return {
          left: 0,
          width: durationInFrames * 1.1 * PIXELS_PER_FRAME * zoomScale
        }
      }

      // 找到最后一个 overlay 的结束位置
      const [, lastOverlayEnd] = getOverlayTimeRange(findLastOverlay(overlays))

      return {
        left: lastOverlayEnd * PIXELS_PER_FRAME * zoomScale,
        width: (durationInFrames - lastOverlayEnd) * PIXELS_PER_FRAME * zoomScale,
      }
    },
    [overlays, zoomScale, durationInFrames]
  )

  // 处理添加内容按钮点击
  const handleAddOverlay = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation()

      if (type === TrackType.STORYBOARD) {
        return appendOverlayToTrack(trackIndex, {
          type: OverlayType.STORYBOARD,
        })
      }

      if (type === TrackType.TEXT && isGlobalTrack) {
        const { playerWidth, playerHeight } = getPlayerDimensions()

        const overlayWidth = playerWidth / 2
        const overlayHeight = overlayWidth / 4
        const overlay: Partial<TextOverlay> = {
          content: '默认文字',
          styles: DEFAULT_TEXT_OVERLAY_STYLES,
          width: overlayWidth,
          height: overlayHeight,
          left: (playerWidth - overlayWidth) / 2,
          top: (playerHeight - overlayHeight) / 2,
        } as const

        return appendOverlayToTrack(trackIndex, {
          ...overlay,
          type: OverlayType.TEXT,
        })
      }
    },
    [type, trackIndex, overlays, playerDimensions, getPlayerDimensions]
  )

  return (
    <div
      className={clsx(
        'absolute inset-y-0 flex items-center px-4',
        'group', // 当悬浮在这个区域时显示
        isDragging && 'hidden', // 拖拽时完全隐藏
      )}
      style={{
        left: styles.left,
        zIndex: 40,
        width: styles.width,
      }}
    >
      {(isGlobalTrack || type === TrackType.STORYBOARD) && (
        <Button
          size="sm"
          variant="ghost"
          className="opacity-0 group-hover:opacity-100 pointer-events-auto bg-white/90 dark:bg-gray-800/90 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-white dark:hover:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-all duration-200"
          onClick={handleAddOverlay}
        >
          <Plus className="w-3 h-3 mr-1" />
          添加{getTrackTypeLabel(type)}
        </Button>
      )}
    </div>
  )
}

export const TimelineTrack: React.FC<TimelineTrackProps> = props => {
  const { type, overlays, trackIndex } = props

  const { selectedOverlay } = useEditorContext()
  const { zoomScale, layout: { getTrackHeight } } = useTimeline()
  const { isDragging, draggingOverlay, landingPoint, mousePosition } = useTimelineDnd()

  // 设置轨道为可放置区域
  const { setNodeRef } = useTypedDroppable(
    EditorDroppableTypes.TimelineTrack,
    trackIndex,
    {
      track: {
        ...props,
        index: trackIndex
      }
    }
  )

  const gaps = findGapsInRow(overlays)

  const renderGhostElement = (
    el: GhostElement | null, isMousePosition = false
  ) => (
    el && el.row === trackIndex && (
      <div
        className={clsx(
          'absolute inset-y-[0.9px] rounded-md border-2 bg-blue-100/30 dark:bg-gray-400/30 shadow-md border-green-500',
          (isMousePosition || el.invalid) && 'border-dashed',
          type === TrackType.NARRATION && {
            'h-[50%]': true,
            'top-[50%]': el.overlay?.type === OverlayType.SOUND,
          },
          el.invalid
            ? 'border-red-500/80'
            : isMousePosition
              ? 'dark:border-white/50 cursor-grabbing'
              : 'border-green-600',
        )}
        style={{
          left: el.from * PIXELS_PER_FRAME * zoomScale,
          width: el.durationInFrames * PIXELS_PER_FRAME * zoomScale,
          zIndex: 50,
        }}
      >
        {isMousePosition && el.overlay && (
          <StyleOnlyTimelineItem
            item={el.overlay}
            width={el.durationInFrames * PIXELS_PER_FRAME * zoomScale}
            className="w-full h-full absolute top-0 left-0 opacity-50"
          />
        )}
      </div>
    )
  )

  const renderItems = useCallback(() => {
    if (type === TrackType.NARRATION) {
      const textOverlays = overlays.filter(o => o.type === OverlayType.TEXT)
      const soundOverlays = overlays.filter(o => o.type === OverlayType.SOUND)
      return (
        <div className="h-full flex flex-col">
          <div className="flex-1 relative">
            {textOverlays.map(overlay => (
              <TimelineItem key={overlay.id} item={overlay} />
            ))}
          </div>
          <div className="flex-1 relative">
            {soundOverlays.map(overlay => (
              <TimelineItem key={overlay.id} item={overlay} />
            ))}
          </div>
        </div>
      )
    }

    return overlays.map(overlay => (
      <TimelineItem key={overlay.id} item={overlay} />
    ))
  }, [overlays, type])

  return (
    <TimelineTrackContext.Provider value={{ currentTrack: { ...props, index: trackIndex } }}>
      <TimelineTrackContextMenu>
        <div
          ref={setNodeRef}
          style={{
            height: getTrackHeight(trackIndex)
          }}
          className={clsx(
            `bg-slate-100/90 dark:bg-gray-800 relative
           transition-all duration-200 ease-in-out select-none
           hover:bg-slate-200/90 dark:hover:bg-gray-700/90`,
            draggingOverlay && (
              !isOverlayAcceptableByTrack(draggingOverlay, { type })
              && 'hover:cursor-not-allowed'
            ),
            {
              'shadow-sm': selectedOverlay && overlays.length,
            },
          )}
        >
          {renderItems()}

          {/* Gap indicators */}
          {!isDragging && gaps.map((gap, gapIndex) => (
            <TrackGapIndicator
              key={`gap-${trackIndex}-${gapIndex}`}
              gap={gap}
              rowIndex={trackIndex}
            />
          ))}

          {/* Ghost element with updated colors - 现在同时支持 TimelineItem 和 Resource 拖拽 */}
          {renderGhostElement(landingPoint)}
          {renderGhostElement(mousePosition, true)}

          <TrailingContainer {...props} />
        </div>
      </TimelineTrackContextMenu>
    </TimelineTrackContext.Provider>
  )
}
