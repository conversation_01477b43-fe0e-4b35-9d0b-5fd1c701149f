import { useState, useMemo } from 'react'
import { useQueryMaterialDirectoryList } from '@/hooks/queries/useQueryMaterial'
import { getPath<PERSON>hain, TreeNode } from '@/components/TreeList'

interface UseFolderDataParams {
  projectId?: number
  treeData?: TreeNode[]
  isSuccess?: boolean
  currentFolderId?: string
  onCurrentFolderIdChange?: (id: string) => void
}

export const useFolderData = ({
  projectId,
  treeData: externalTreeData,
  isSuccess: externalIsSuccess,
  currentFolderId: externalCurrentFolderId,
  onCurrentFolderIdChange
}: UseFolderDataParams) => {
  const [internalFolderId, setInternalFolderId] = useState<string>('')

  const currentFolderId = externalCurrentFolderId ?? internalFolderId

  // 用于更新 currentFolderId（受控/非受控都兼容）
  const setCurrentFolderId = (id: string) => {
    if (onCurrentFolderIdChange) {
      onCurrentFolderIdChange(id) // 受控模式下通知外部
    }
    if (externalCurrentFolderId === undefined) {
      setInternalFolderId(id) // 非受控模式下更新内部状态
    }
  }
  const queryEnabled = !!projectId && externalTreeData === undefined
  const { data: queryTreeData, isSuccess: queryIsSuccess } = useQueryMaterialDirectoryList(
    { projectId: projectId! },
    { enabled: queryEnabled },
  )

  // 选择数据来源（优先外部传入）
  const treeData = externalTreeData ?? queryTreeData
  const isSuccess = externalIsSuccess ?? queryIsSuccess

  // 计算路径
  const folderPath = useMemo(() => {
    if (!treeData || !currentFolderId) return []
    return getPathChain(treeData, currentFolderId) ?? [] // 递归计算路径
  }, [treeData, currentFolderId])

  // 获取当前目录下的子目录
  const childFolders = useMemo(() => {
    if (!treeData || !currentFolderId) return []

    const findChildren = (nodes: any[]): TreeNode[] => {
      for (const node of nodes) {
        if (String(node.id) === String(currentFolderId)) {
          return node.children || []
        }

        if (node.children && node.children.length > 0) {
          const result = findChildren(node.children)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }

    return findChildren(treeData)
  }, [treeData, currentFolderId])

  return {
    treeData,
    currentFolderId,
    setCurrentFolderId,
    folderPath,
    childFolders,
    isSuccess,
  }
}
