import React, { createContext, useContext, useState, ReactNode } from 'react'
import { DetailDOS, CreateMatrixParams, Account } from '@/types/matrix/douyin'

// 扩展的预览数据类型，包含完整的账号信息
export interface PreviewMatrixParams extends CreateMatrixParams {
  accounts: Account[] // 完整的账号信息，用于预览显示
}

interface PreviewPublishContextType {
  previewData: PreviewMatrixParams | null
  setPreviewData: (data: PreviewMatrixParams | null) => void
  detailDOS: DetailDOS[]
  setDetailDOS: (details: DetailDOS[]) => void
}

const PreviewPublishContext = createContext<PreviewPublishContextType | undefined>(undefined)

export const usePreviewPublish = () => {
  const context = useContext(PreviewPublishContext)
  if (!context) {
    throw new Error('usePreviewPublish must be used within a PreviewPublishProvider')
  }
  return context
}

interface PreviewPublishProviderProps {
  children: ReactNode
}

export const PreviewPublishProvider: React.FC<PreviewPublishProviderProps> = ({ children }) => {
  const [previewData, setPreviewData] = useState<PreviewMatrixParams | null>(null)
  const [detailDOS, setDetailDOS] = useState<DetailDOS[]>([])

  return (
    <PreviewPublishContext.Provider
      value={{
        previewData,
        setPreviewData,
        detailDOS,
        setDetailDOS,
      }}
    >
      {children}
    </PreviewPublishContext.Provider>
  )
}
