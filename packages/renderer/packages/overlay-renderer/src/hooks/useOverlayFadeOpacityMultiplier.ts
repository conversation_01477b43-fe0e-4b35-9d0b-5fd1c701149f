import { Overlay } from '@clipnest/remotion-shared/types'
import { useCurrentFrame } from 'remotion'
import { useMemo } from 'react'
import { useRenderContext } from '../render.context'

export function useOverlayFadeOpacityMultiplier(overlay: Overlay) {
  const frame = useCurrentFrame()
  const { playerMetadata: { fps } } = useRenderContext()

  return useMemo(() => {
    // 计算淡入淡出的帧数
    const fadeInFrames = overlay.fadeInDuration ? Math.round(overlay.fadeInDuration * fps) : 0
    const fadeOutFrames = overlay.fadeOutDuration ? Math.round(overlay.fadeOutDuration * fps) : 0

    // 计算淡入淡出的不透明度
    let fadeInOpacity = 1
    let fadeOutOpacity = 1

    // 淡入效果：在开始的 fadeInFrames 帧内，不透明度从 0 逐渐增加到 1
    if (fadeInFrames > 0 && frame < fadeInFrames) {
      fadeInOpacity = frame / fadeInFrames
    }

    // 淡出效果：在结束的 fadeOutFrames 帧内，不透明度从 1 逐渐减少到 0
    if (fadeOutFrames > 0 && frame >= overlay.durationInFrames - fadeOutFrames) {
      fadeOutOpacity = (overlay.durationInFrames - frame) / fadeOutFrames
    }

    // 将淡入和淡出的不透明度相乘，得到最终的不透明度
    // 这样即使淡入和淡出时间重叠，也能得到正确的效果
    let fadeOpacity = fadeInOpacity * fadeOutOpacity

    // 确保不透明度在 0-1 范围内
    fadeOpacity = Math.max(0, Math.min(1, fadeOpacity))

    return fadeOpacity
  }, [frame, fps, overlay.fadeInDuration, overlay.fadeOutDuration])
}
