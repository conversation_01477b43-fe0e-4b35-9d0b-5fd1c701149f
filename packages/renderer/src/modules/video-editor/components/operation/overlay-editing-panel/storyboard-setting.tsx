import React, { useEffect, useMemo, useState } from 'react'
import { useEditorContext, useOverlayEditing } from '@/modules/video-editor/contexts'
import { StoryboardOverlay } from '@clipnest/remotion-shared/types'
import { TrackType } from '@/modules/video-editor/types'
import { Input } from '@/components/ui/input'
import { FPS } from '@/modules/video-editor/constants'
import * as _ from 'lodash'
import { useStoryboardHelper } from '@/modules/video-editor/hooks/helpers/useStoryboardHelper'

export const StoryboardSetting: React.FC = () => {
  const { tracks, } = useEditorContext()
  const { localOverlay, updateEditingOverlay } = useOverlayEditing<StoryboardOverlay>()
  const { updateStoryboardDuration } = useStoryboardHelper()

  const [storyboard, index] = useMemo(() => {
    const storyboards = tracks
      .find(t => t.type === TrackType.STORYBOARD)
      ?.overlays
      || []

    const index = storyboards.findIndex(o => o.id === localOverlay.id) ?? -1

    return [storyboards[index] as StoryboardOverlay | null, index]
  }, [tracks, localOverlay])

  const [storyboardTitle, setStoryboardTitle] = useState(storyboard?.title)
  const [storyboardDuration, setStoryboardDuration] = useState<number>()

  useEffect(() => {
    if (storyboard) {
      setStoryboardDuration(_.round(storyboard.durationInFrames / FPS, 2))
    }
  }, [storyboard])

  if (!storyboard || index === -1) return null

  return (
    <div className="flex flex-col gap-3">

      <div className="grid grid-cols-2 text-sm gap-4 text-gray-400">
        <span>分镜号</span>
        <div>#{index + 1}</div>

        <span>分镜名称</span>
        <div>
          <Input
            value={storyboardTitle}
            onChange={e => setStoryboardTitle(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault()
                updateEditingOverlay({ title: storyboardTitle }, true)
              }
            }}
            onBlur={() => {
              updateEditingOverlay({ title: storyboardTitle }, true)
            }}
          />
        </div>

        <span>分镜时长</span>
        <div className="flex items-center gap-2">
          <Input
            type="number"
            className="w-20"
            min={1}
            value={storyboardDuration}
            onChange={e => {
              const newDuration = parseFloat(e.target.value)
              if (!isNaN(newDuration)) {
                setStoryboardDuration(newDuration)
              }
            }}
            onBlur={() => {
              if (storyboardDuration) {
                updateStoryboardDuration(localOverlay.id, Math.round(storyboardDuration * FPS))
              }
            }}
            onKeyDown={e => {
              if (e.key === 'Enter' && storyboardDuration) {
                e.preventDefault()
                updateStoryboardDuration(localOverlay.id, Math.round(storyboardDuration * FPS))
              }
            }}
          />
          <span>秒</span>
        </div>
      </div>
    </div>
  )
}

