import React from 'react'
import { registerResourcePlugin } from '../registry'
import { Type } from 'lucide-react'
import { ResourcePlugins } from '../types'

const Panel = React.lazy(() =>
  import('@/modules/video-editor/resource-plugin-system/plugin-panels/text.panel')
)

export default registerResourcePlugin({
  id: ResourcePlugins.TEXT,
  title: '文字',
  icon: Type,
  component: Panel,
  order: 90,
})

