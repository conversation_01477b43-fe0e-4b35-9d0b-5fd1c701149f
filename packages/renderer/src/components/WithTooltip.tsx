import React from 'react'
import { Tooltip, TooltipContent, TooltipTrigger, } from '@/components/ui/tooltip'

export interface WithTooltipProps {
  /**
   * 要显示的内容
   */
  content: React.ReactNode
  /**
   * 可选的快捷键文本
   */
  shortcut?: string | string[]
  /**
   * 子元素
   */
  children: React.ReactNode
  /**
   * Tooltip 显示位置
   */
  side?: 'top' | 'right' | 'bottom' | 'left'
  /**
   * Tooltip 对齐方式
   */
  align?: 'start' | 'center' | 'end'
  /**
   * Tooltip 与触发器的距离
   */
  sideOffset?: number
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 是否禁用 tooltip
   */
  disabled?: boolean
}

/**
 * 通用 Tooltip 包装组件
 *
 * @example
 * ```tsx
 * <WithTooltip content="撤销" shortcut="Ctrl+Z">
 *   <Undo2 className="h-3.5 w-3.5" />
 * </WithTooltip>
 * ```
 */
export function WithTooltip({
  content,
  shortcut,
  children,
  side = 'top',
  align = 'start',
  sideOffset = 5,
  disabled = false,
}: WithTooltipProps) {
  if (disabled) {
    return <>{children}</>
  }

  const shortcutKeys = typeof shortcut === 'string' ? [shortcut] : shortcut

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        {children}
      </TooltipTrigger>
      <TooltipContent
        side={side}
        align={align}
        sideOffset={sideOffset}
      >
        <div className="flex items-center gap-1">
          <span className="text-gray-700 dark:text-zinc-200">{content}</span>

          <div className="flex items-center gap-0.5">
            {shortcutKeys?.map(key => (
              <kbd className="px-1 py-0.5 text-[10px] font-mono bg-gray-800 dark:bg-gray-800 text-white rounded-md border border-gray-700">
                {key}
              </kbd>
            ))}
          </div>
        </div>
      </TooltipContent>
    </Tooltip>
  )
}
