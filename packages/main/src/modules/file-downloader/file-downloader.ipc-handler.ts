import { Injectable, Inject } from '@nestjs/common'
import { FileDownloaderService } from './file-downloader.service.js'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'

@Injectable()
export class FileDownloaderIPCHandler extends Base<PERSON>CHandler<'fileDownloader'> {

  protected readonly platformPrefix = 'fileDownloader'

  constructor(@Inject(FileDownloaderService) private fileDownloaderService: FileDownloaderService) {
    super()
  }

  /**
   * 注册所有 IPC 处理程序
   */
  registerAll(): void {
    this.registerHandler('downloadFiles', this.fileDownloaderService.downloadFiles.bind(this.fileDownloaderService))
    this.registerHandler('fileExists', this.fileDownloaderService.fileExists.bind(this.fileDownloaderService))
    this.registerHandler('selectFile', this.fileDownloaderService.selectFile.bind(this.fileDownloaderService))
    this.registerHandler('selectFolder', this.fileDownloaderService.selectFolder.bind(this.fileDownloaderService))
  }
}
