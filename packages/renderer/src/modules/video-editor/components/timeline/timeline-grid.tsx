/**
 * TimelineGrid Component
 * Renders a grid-based timeline view for managing overlay items across multiple rows.
 * Supports drag and drop, resizing, and various item management operations.
 */

import React from 'react'
import { SNAPPING_CONFIG } from '../../constants'
import { TimelineTrack } from './timeline-track'
import { useEditorContext, useTimeline, useTimelineDnd } from '@/modules/video-editor/contexts'

/**
 * TimelineGrid component that displays overlay items in a row-based timeline view
 */
export const TimelineGrid: React.FC = () => {
  const { tracks, durationInFrames } = useEditorContext()

  const {
    timelineGridRef,
    layout: { totalHeight, rowGap },
  } = useTimeline()

  const { isDragging, alignmentLines } = useTimelineDnd()

  return (
    <div
      ref={timelineGridRef}
      id="TimelineGrid"
      className="relative overflow-y-hidden h-full"
      style={{
        height: totalHeight,
        scrollbarWidth: 'none',
      }}
    >
      {/* Container for Rows and Alignment Lines */}
      <div
        className="absolute inset-0 flex flex-col py-2"
        style={{ rowGap }}
      >
        {/* Render Alignment Lines - Conditionally visible and higher contrast */}
        {isDragging
            && SNAPPING_CONFIG.enableVerticalSnapping
            && alignmentLines.map(frame => (
              <div
                key={`align-${frame}`}
                className="absolute top-0 bottom-0 w-px border-r border-dashed border-gray-500 dark:border-gray-200 z-40 pointer-events-none"
                style={{
                  left: `${(frame / durationInFrames) * 100}%`,
                  height: '100%',
                }}
                aria-hidden="true"
              />
            ))}

        {/* Render Tracks */}
        {tracks.map((track, trackIndex) => (
          <TimelineTrack
            key={trackIndex}
            trackIndex={trackIndex}
            {...track}
          />
        ))}
      </div>
    </div>
  )
}
