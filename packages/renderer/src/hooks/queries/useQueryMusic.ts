import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'
import { useInfiniteQuery } from '../useInfiniteQuery'
import { BaseResourceQueryParams, SoundResource } from '@/types/resources'
import { buildTreeFromFlatList } from '@/components/TreeList'

export const useQueryMusicCategory = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.MUSIC_CATEGORY],
    queryFn: () => ResourceModule.music.category(),
  })
}

export const useQueryMusicDirList = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.LOCAL_MUSIC_FOLDER_LIST],
    queryFn: async () => {
      const flatList = await ResourceModule.music.dirList()
      return buildTreeFromFlatList(flatList)
    },
  })
}

/**
 * 使用无限查询获取音乐本地资源列表，支持无限滚动加载
 * @param params 查询参数，包括文件夹ID和每页大小
 * @returns 无限查询结果
 */
export const useInfiniteQueryLocalMusicList = (
  params: BaseResourceQueryParams & { folderUuid: string },
) => {
  return useInfiniteQuery<SoundResource.Sound>(
    [QUERY_KEYS.LOCAL_SOUND_LIST],
    ResourceModule.music.localList,
    params,
    {
      pageSize: params.pageSize || 12,
      enabled: params.folderUuid !== '',
    },
  )
}

export const useInfiniteQueryMusicRankList = (params: BaseResourceQueryParams & { enabled?: boolean }) => {
  const { enabled, ...queryParams } = params
  return useInfiniteQuery<SoundResource.Sound>(
    [QUERY_KEYS.MUSIC_RANK],
    ResourceModule.music.rank,
    queryParams,
    {
      pageSize: queryParams.pageSize || 20,
      enabled: enabled !== undefined ? enabled : true,
    }
  )
}

export const useInfiniteQueryMusicUnified = (params: BaseResourceQueryParams & { selectedCategory?: string; enabled?: boolean }) => {
  const { selectedCategory, enabled, ...queryParams } = params

  // 判断是否为收藏分类
  const isCollectedCategory = selectedCategory === 'collected'

  // 根据分类决定查询参数
  const finalParams = isCollectedCategory
    ? queryParams // 收藏列表不需要 categoryIds
    : {
      ...queryParams,
      // 全部分类不传 categoryIds，其他分类传递对应的 categoryIds
      categoryIds: (!selectedCategory || selectedCategory === 'all')
        ? undefined
        : [selectedCategory]
    }

  // 根据分类决定使用哪个查询
  if (isCollectedCategory) {
    return useInfiniteQuery<SoundResource.Sound>(
      [QUERY_KEYS.MUSIC_COLLECTED, finalParams],
      ResourceModule.music.collected,
      finalParams,
      {
        pageSize: finalParams.pageSize || 20,
        enabled: enabled !== undefined ? enabled : true,
      }
    )
  } else {
    return useInfiniteQuery<SoundResource.Sound>(
      [QUERY_KEYS.MUSIC_LIST, finalParams],
      ResourceModule.music.list,
      finalParams,
      {
        pageSize: finalParams.pageSize || 20,
        enabled: enabled !== undefined ? enabled : (finalParams.categoryIds ? finalParams.categoryIds.length > 0 : true)
      }
    )
  }
}

