import React from 'react'
import { cn } from '@/components/lib/utils'

export interface AudioProgressProps {
  /**
   * 当前播放时间（秒）
   */
  currentTime: number
  /**
   * 音频总时长（秒）
   */
  duration: number
  /**
   * 是否正在播放
   */
  isPlaying: boolean
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 音频播放进度遮罩组件
 * 显示黑色遮罩覆盖未播放部分，已播放部分透明
 */
export function AudioProgress({
  currentTime,
  duration,

  className = ''
}: AudioProgressProps) {
  // 计算进度百分比
  const progress = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className={cn('absolute inset-0 pointer-events-none', className)}>
      {/* 未播放部分的黑色遮罩 */}
      <div
        className="absolute top-0 right-0 bottom-0 bg-black/60 transition-all duration-100"
        style={{
          width: `${Math.max(0, Math.min(100, 100 - progress))}%`,
          transition: 'width 0.1s ease-out'
        }}
      />
    </div>
  )
}
