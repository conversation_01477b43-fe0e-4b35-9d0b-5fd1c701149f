import React, { <PERSON>actNode, useState } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { ResourceCacheIndicator } from '@/modules/video-editor/resource-plugin-system/components/resource-cache-indicator'
import { CloudResourceTypes, SoundResource } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import { ResourceCollectionIndicator } from '@/modules/video-editor/components/common/resource-collection-indicator'
import { ImageOff, Pause, Play } from 'lucide-react'
import { AudioProgress } from '@/modules/video-editor/components/common/audio-progress-bar'
import { cacheManager } from '@/libs/cache/cache-manager'
import { useResourceCacheStatus } from '../../hooks/resource/useResourceCacheStatus'
import { EditorDraggableTypes, useTypedDraggable } from '@/modules/video-editor/components/editor-dnd-wrapper'
import { useAudioPlayer } from '../../hooks/useAudioPlayer'

export class NativeAudioManager {

  private static instance: NativeAudioManager
  private currentAudio: HTMLAudioElement | null = null
  private currentId: string | number | null = null
  private listeners: Map<string | number, (event: 'play' | 'pause' | 'stop') => void> = new Map()

  static getInstance(): NativeAudioManager {
    if (!NativeAudioManager.instance) {
      NativeAudioManager.instance = new NativeAudioManager()
    }
    return NativeAudioManager.instance
  }

  /**
   * 注册音频实例的事件监听器
   */
  registerListener(id: string | number, callback: (event: 'play' | 'pause' | 'stop') => void) {
    this.listeners.set(id, callback)
  }

  /**
   * 移除音频实例的事件监听器
   */
  unregisterListener(id: string | number) {
    this.listeners.delete(id)
  }

  /**
   * 播放指定音频，停止其他正在播放的音频
   */
  play(audio: HTMLAudioElement, id: string | number) {
    // 如果有其他音频正在播放，先停止它
    if (this.currentAudio && this.currentId !== id) {
      this.currentAudio.pause()
      // 通知之前播放的音频实例
      const prevListener = this.listeners.get(this.currentId!)
      if (prevListener) {
        prevListener('stop')
      }
    }

    this.currentAudio = audio
    this.currentId = id

    audio.play().catch(err => {
      console.error('播放音频失败:', err)
      // 通知当前音频实例播放失败
      const listener = this.listeners.get(id)
      if (listener) {
        listener('stop')
      }
    })
  }

  /**
   * 暂停指定音频
   */
  pause(id: string | number) {
    if (this.currentId === id && this.currentAudio) {
      this.currentAudio.pause()
    }
  }

  /**
   * 停止指定音频
   */
  stop(id: string | number) {
    if (this.currentId === id && this.currentAudio) {
      this.currentAudio.pause()
      this.currentAudio.currentTime = 0
      this.currentAudio = null
      this.currentId = null
    }
  }

  /**
   * 检查指定音频是否正在播放
   */
  isPlaying(id: string | number): boolean {
    return this.currentId === id && !!this.currentAudio && !this.currentAudio.paused
  }
}

export interface AudioResourceItemProps {
  item: SoundResource.Sound

  /**
   * 默认图标
   */
  icon?: ReactNode

  /**
   * 自定义内容
   */
  children?: ReactNode

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 资源类型，用于检查本地缓存
   */
  resourceType: ResourceType

  /**
   * 是否显示收藏按钮
   */
  showCollectionButton?: boolean

  /**
   * 收藏状态变更回调
   */
  onCollectionChange?: (collected: boolean) => void
}

interface BaseAudioResourceCardProps {
  id: string | number
  title?: string
  description?: string
  thumbnailUrl?: string
  icon?: ReactNode
  isMaterial?: boolean // 是否是素材库
  getAudioSrc: () => string
  isCached: boolean
  resourceType: ResourceType
  audioUrl: string
  showCacheIndicator?: ReactNode
  showCollectionIndicator?: ReactNode
}

export function BaseAudioResourceCard({
  id,
  title,
  description,
  thumbnailUrl,
  icon,
  isMaterial = false,
  getAudioSrc,
  isCached,
  resourceType,
  audioUrl,
  showCacheIndicator,
  showCollectionIndicator,
}: BaseAudioResourceCardProps) {
  const [imageLoadError, setImageLoadError] = useState(false)
  const { isPlaying, currentTime, duration, showProgressBar, handleTogglePlay } = useAudioPlayer(
    isCached,
    id,
    getAudioSrc,
  )

  return (
    <div
      className={cn(
        `group relative w-full h-full
          bg-muted/30
          rounded dark:bg-gray-800/40
          border dark:border-gray-700/10
          hover:border-blue-500/20 dark:hover:border-blue-500/20
          hover:bg-blue-500/5 dark:hover:bg-blue-500/5
          transition-all overflow-hidden`,
        isCached ? 'border-green-500/20 dark:border-green-500/20' : '',
        resourceType && audioUrl ? 'cursor-grab active:cursor-grabbing' : '',
        // 播放时显示橙色边框
        showProgressBar ? 'border-orange-500/60 dark:border-orange-500/60' : '',
      )}
      draggable={!!resourceType && !!audioUrl}
    >
      {description && !isMaterial && (
        <div className="text-[10px] border border-neutral-800 bg-neutral-900/80 rounded px-1 h-4 flex items-center justify-center text-muted absolute bottom-2 left-2 z-10">
          {description}
        </div>
      )}

      {thumbnailUrl ? (
        <div className="absolute inset-0 flex items-center justify-center">
          {imageLoadError ? (
            <div className="flex items-center justify-center text-gray-400">
              <ImageOff className="w-8 h-8" />
            </div>
          ) : (
            <img
              src={thumbnailUrl}
              alt={title}
              className="max-w-full max-h-full object-contain"
              loading="lazy"
              onError={() => setImageLoadError(true)}
              onLoad={() => setImageLoadError(false)}
            />
          )}
        </div>
      ) : (
        icon && <div className="absolute inset-0 flex items-center justify-center text-gray-400">{icon}</div>
      )}

      {/* 播放/暂停按钮 - 悬浮显示，播放时默认显示 */}
      {audioUrl && (
        <button
          onClick={handleTogglePlay}
          className={cn(
            'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
            'rounded-full p-3 shadow-lg cursor-pointer',
            'bg-black/70 hover:bg-black/90 text-white',
            'transition-all duration-200 z-30',
            'opacity-0 group-hover:opacity-100',
            isPlaying && 'opacity-100',
          )}
        >
          {isPlaying ? <Pause size={20} /> : <Play size={20} />}
        </button>
      )}

      {/* 播放进度指示器 - 根据图片样式，显示橙色进度 */}
      {showProgressBar && audioUrl && duration > 0 && (
        <>
          {/* 竖直进度指示器 - 跟随播放进度从左到右移动 */}
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-orange-500 z-25 transition-all duration-100"
            style={{
              left: `${duration > 0 ? (currentTime / duration) * 100 : 0}%`,
              boxShadow: '0 0 4px rgba(249, 115, 22, 0.5)'
            }}
          />

          {/* 播放进度遮罩 */}
          <AudioProgress currentTime={currentTime} duration={duration} isPlaying={isPlaying} className="" />
        </>
      )}

      {showCacheIndicator}
      {showCollectionIndicator}
    </div>
  )
}

export function getAvailableSrcPath(
  audioUrl: string,
  resourceType: ResourceType,
  isCached: boolean
): string {
  let srcPath = audioUrl

  if (isCached && resourceType && audioUrl) {
    const localPath = cacheManager.resource.getResourcePathSync(resourceType, audioUrl)
    srcPath = localPath || audioUrl
  }

  return srcPath
}

function AudioResourceContent({
  item,
  icon,
  children,
  className = '',
  resourceType,
  showCollectionButton = true,
  onCollectionChange,
}: AudioResourceItemProps) {
  const { interactInfo, title, id } = item
  const audioUrl = item.content.itemUrl
  const thumbnailUrl = item.cover?.url || ''
  const description = (item.content.durationMsec / 1000).toFixed(1) + 's'

  const { isCached, isChecking } = useResourceCacheStatus(resourceType, audioUrl)

  const isCollected = interactInfo?.collected || false

  const cacheIndicator = resourceType && audioUrl && (
    <ResourceCacheIndicator
      resourceType={resourceType}
      resourceUrl={audioUrl}
      size={12}
      isCached={isCached}
      isChecking={isChecking}
    />
  )

  const collectionIndicator = resourceType && id && showCollectionButton && (
    <div
      className={cn(
        'absolute right-0 top-0 transition-opacity duration-200',
        isCollected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100',
      )}
    >
      <ResourceCollectionIndicator
        resourceType={resourceType}
        resourceId={id}
        isCollected={isCollected}
        size={12}
        onCollectionChange={onCollectionChange}
      />
    </div>
  )

  return (
    <div className={`aspect-square ${className}`}>
      <BaseAudioResourceCard
        id={id}
        title={title}
        description={description}
        thumbnailUrl={thumbnailUrl}
        icon={icon}
        getAudioSrc={() => getAvailableSrcPath(audioUrl, resourceType, isCached)}
        isCached={isCached}
        resourceType={resourceType}
        audioUrl={audioUrl}
        showCacheIndicator={cacheIndicator}
        showCollectionIndicator={collectionIndicator}
      />

      {/* 子内容 */}
      {children}

      {/* 标题 */}
      {title && <div className="text-xs w-full text-neutral-300 truncate mt-2">{title}</div>}
    </div>
  )
}

export const AudioResourceItem: React.FC<AudioResourceItemProps> = props => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(EditorDraggableTypes.Resource, props.item.id, {
    resourceType: CloudResourceTypes.SOUND,
    data: props.item,
  })

  return (
    <div ref={setNodeRef} {...listeners} {...attributes}>
      <AudioResourceContent {...props} />
    </div>
  )
}
