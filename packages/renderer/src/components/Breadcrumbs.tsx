import React from 'react'
import { cn } from '@/components/lib/utils'
import { TreeNode } from '@/components/TreeList'

interface BreadcrumbsProps {
  folderPath: TreeNode[]
  currentFolderId: string
  onFolderClick: (folderId: string) => void
}

/**
 * 文件夹目录面包屑
 */
const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ folderPath, currentFolderId, onFolderClick }) => {
  return (
    <div className="flex items-center space-x-1">
      {folderPath.map((folder, index) => (
        <React.Fragment key={folder.id}>
          <button
            className={cn('hover:underline', {
              'text-primary-highlight1': folder.id === currentFolderId,
            })}
            onClick={() => onFolderClick(folder.id)}
          >
            {folder.label}
          </button>
          {index < folderPath.length - 1 && <span>{'>'}</span>}
        </React.Fragment>
      ))}
    </div>
  )
}

export default Breadcrumbs
