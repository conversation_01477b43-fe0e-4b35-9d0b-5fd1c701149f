import React from 'react'
import { FolderOpen } from 'lucide-react'
import { registerResourcePlugin } from '../registry'
import { ResourcePlugins } from '../types'

const Panel = React.lazy(() =>
  import('@/modules/video-editor/resource-plugin-system/plugin-panels/material-lib.panel')
)

export default registerResourcePlugin({
  id: ResourcePlugins.MATERIAL_LIB,
  title: '素材库',
  icon: FolderOpen,
  component: Panel,
  order: 1,
})
