import { <PERSON><PERSON><PERSON>and<PERSON> } from '@/infra/types/BaseIPCHandler.js'
import { RequestService } from './request.service.js'
import { Inject } from '@nestjs/common'
import { IPCRequestState } from '@app/shared/types/auth-request.types.js'

export class BaseInfoIP<PERSON>andler extends BaseIPCHandler<'baseInfo'> {

  protected readonly platformPrefix = 'baseInfo'

  constructor(@Inject(RequestService) private readonly requestService: RequestService) {
    super()
  }

  /**
   * 注册所有 IPC 处理程序
   */
  registerAll(): void {
    this.registerHandler('updateRequestState', this.requestService.updateRequestState.bind(this.requestService))

    this.registerHandler('getRequestState', this.requestService.getState.bind(this.requestService))

    this.registerHandler('clearRequestState', this.requestService.clearState.bind(this.requestService))

    this.registerHandler('isTokenExpired', this.requestService.isTokenExpired.bind(this.requestService))
  }
}
