import * as React from 'react'
import { cn } from '@/components/lib/utils'
import { ChevronDown, ChevronUp } from 'lucide-react'

export interface NumberInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value: number
  onChange?: (value: number) => void
}

const NumberInput = React.forwardRef<HTMLInputElement, NumberInputProps>(
  (
    {
      className,
      step = 1,
      min,
      max,
      value,
      onChange,
      ...props
    },
    ref
  ) => {
    const inputRef = React.useRef<HTMLInputElement>(null)

    React.useImperativeHandle(ref, () => inputRef.current!)

    const parseValue = (val: string) => {
      if (val === '' || val === '-') return 0
      const num = parseFloat(val)
      return isNaN(num) ? 0 : num
    }

    const clampValue = (val: number | null): number  => {
      if (val === null) return 0
      let clamped = val
      if (typeof min === 'number') clamped = Math.max(clamped, min)
      if (typeof max === 'number') clamped = Math.min(clamped, max)
      return clamped
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const parsed = parseValue(e.target.value)
      onChange?.(parsed)
    }

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      const parsed = parseValue(e.target.value) ?? 0
      const clamped = clampValue(parsed)
      if (clamped !== parsed) {
        onChange?.(clamped)
      }
      props.onBlur?.(e)
    }

    const handleStep = (direction: 'up' | 'down') => {
      const current = typeof value === 'number' ? value : 0
      let next = direction === 'up' ? current + Number(step) : current - Number(step)

      if (typeof min === 'number') next = Math.max(next, min)
      if (typeof max === 'number') next = Math.min(next, max)

      onChange?.(next)
    }

    return (
      <div className="relative flex ">
        <input
          type="number"
          ref={inputRef}
          step={step}
          min={min}
          max={max}
          value={value ?? ''}
          onChange={handleInputChange}
          onBlur={handleBlur}
          className={cn(
            'flex h-8 w-full appearance-none rounded-md border border-input bg-transparent px-2 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50' +
            ' [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:m-0 [&::-webkit-outer-spin-button]:m-0',
            className
          )}
          {...props}
        />
        <div className="absolute right-1 top-1/2 -translate-y-1/2 flex flex-col gap-px">
          <button
            type="button"
            onClick={() => handleStep('up')}
            className="p-0.5 text-muted-foreground hover:text-foreground transition-colors"
            tabIndex={-1}
          >
            <ChevronUp className="h-3 w-3" />
          </button>
          <button
            type="button"
            onClick={() => handleStep('down')}
            className="p-0.5 text-muted-foreground hover:text-foreground transition-colors"
            tabIndex={-1}
          >
            <ChevronDown className="h-3 w-3" />
          </button>
        </div>

        {/* 隐藏原生箭头 */}

      </div>
    )
  }
)

NumberInput.displayName = 'NumberInput'

export { NumberInput }

// 范围输入组件接口
export interface RangeInputProps {
  minValue: number
  maxValue: number
  onMinChange?: (value: number) => void
  onMaxChange?: (value: number) => void
  min?: number
  max?: number
  step?: number
  className?: string
  placeholder?: string
  suffix?: string
}

// 范围输入组件
export const RangeInput = React.forwardRef<HTMLDivElement, RangeInputProps>(
  function RangeInput(
    {
      minValue,
      maxValue,
      onMinChange,
      onMaxChange,
      min = 0,
      max = 100,
      step = 1,
      className,
      suffix = ''
    },
    ref
  ) {
    const clampValue = (val: number): number => {
      return Math.max(min, Math.min(max, val))
    }

    const handleMinChange = (value: number) => {
      const clamped = clampValue(value)
      const finalValue = Math.min(clamped, maxValue)
      onMinChange?.(finalValue)
    }

    const handleMaxChange = (value: number) => {
      const clamped = clampValue(value)
      const finalValue = Math.max(clamped, minValue)
      onMaxChange?.(finalValue)
    }

    return (
      <div ref={ref} className={cn('flex items-center gap-1', className)}>
        <NumberInput
          value={minValue}
          onChange={handleMinChange}
          min={min}
          max={max}
          step={step}
          className="w-16"
        />
        <span className="text-muted-foreground text-sm">-</span>
        <NumberInput
          value={maxValue}
          onChange={handleMaxChange}
          min={min}
          max={max}
          step={step}
          className="w-16"
        />
        {suffix && <span className="text-muted-foreground text-sm">{suffix}</span>}
      </div>
    )
  }
)
