import { lazy } from 'react'
import { create } from 'zustand'
import { nanoid } from 'nanoid'
import { persist } from 'zustand/middleware'
import { pick } from 'lodash'
import { z } from 'zod'
import { MixcutPageTabs } from '@/contexts'

export const VirtualizableComponents = {
  Mixcut: lazy(() => import('@/modules/mixcut/mixcut.page')),
  Editor: lazy(() => import('@/pages/virtualizable/VideoEditor')),
  Script: lazy(() => import('@/pages/virtualizable/ScriptEdit')),
} as const

export type VirtualizableComponentKeys = keyof typeof VirtualizableComponents

// 组件标题映射表
const ComponentTitleMap = {
  Editor: '视频编辑',
  Mixcut: '视频混剪',
  Script: '脚本编辑',
} as const

// 参数验证模式映射
const ComponentParamsSchemas = {
  Editor: z.object({
    id: z.string().min(1, 'ID不能为空'),
    projectId: z.string().min(1, 'projectId 不能为空'),
    name: z.string().optional(), // 用于显示在标题中
  }),
  Mixcut: z.object({
    id: z.string().min(1, 'ID不能为空'),
    name: z.string().optional(), // 用于显示在标题中
    defaultTab: z.custom<MixcutPageTabs>().optional(), // 默认选项卡
  }),
  Script: z.object({
    id: z.string().min(1, 'ID不能为空'),
  }),
} as const

// 类型安全的参数映射
export type ParamsForComponent<T extends VirtualizableComponentKeys> = z.infer<typeof ComponentParamsSchemas[T]>

export interface VirtualTab {
  id: string
  componentKey: VirtualizableComponentKeys
  title: string
  keepAlive?: boolean
  closable?: boolean
  params: Record<string, any>

  // icon?: React.ReactNode

  /**
   * @deprecated
   */
  component?: never
}

interface VirtualTabsStore {
  tabs: VirtualTab[]
  activeTabId: string | null

  // 操作方法
  goToHomePage(): void
  pushNamedTab<T extends VirtualizableComponentKeys>(name: T, params: ParamsForComponent<T>): string | undefined
  closeTab(id: string): void
  setActiveTab(id: string): void
  updateTab(id: string, updates: Partial<Omit<VirtualTab, 'id'>>): void

  // 批量操作
  closeOtherTabs(id: string): void
  closeAllTabs(): void
  closeTabsToRight(id: string): void
}

/**
 * 验证组件参数
 * @param componentKey 组件键名
 * @param params 参数对象
 * @returns 验证结果
 */
function validateComponentParams<T extends VirtualizableComponentKeys>(
  componentKey: T,
  params: unknown
): { success: true; data: ParamsForComponent<T> } | { success: false; error: string } {
  try {
    const schema = ComponentParamsSchemas[componentKey]
    const validatedParams = schema.parse(params)
    return { success: true, data: validatedParams as ParamsForComponent<T> }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      return { success: false, error: errorMessage }
    }
    return { success: false, error: '参数验证失败' }
  }
}

const wrappedPersist: typeof persist =
  import.meta.env.DEV && import.meta.env.VITE_APP_ENABLE_VIRTUAL_TAB_CACHE
    ? persist
    : (fn: any) => fn

const useVirtualTabsStore = create<VirtualTabsStore>()(
  wrappedPersist(
    (set, get) => ({
      tabs: [],
      activeTabId: null,

      goToHomePage: () => set({ activeTabId: null }),

      pushNamedTab: <T extends VirtualizableComponentKeys>(name: T, params: ParamsForComponent<T>) => {
        // 验证参数
        const validation = validateComponentParams(name, params)
        if (!validation.success) {
          console.error(`参数验证失败: ${validation.error}`)
          return undefined
        }

        const validatedParams = validation.data

        // 直接生成标签页 ID 和标题
        const tabId = 'id' in validatedParams && validatedParams.id
          ? `${name}-${validatedParams.id}`
          : `${name}-${nanoid()}`

        const baseTitle = ComponentTitleMap[name]
        const title = 'name' in validatedParams && validatedParams.name
          ? `${baseTitle} - ${validatedParams.name}`
          : baseTitle

        const newTab: VirtualTab = {
          id: tabId,
          componentKey: name,
          title,
          params: validatedParams,
          closable: true,
        }

        // 检查是否已存在相同 ID 的标签
        const { tabs } = get()
        const existingTabIndex = tabs.findIndex(t => t.id === tabId)

        set(state => {
          // 如果标签已存在，则激活它
          if (existingTabIndex !== -1) {
            return { ...state, activeTabId: tabId }
          }

          // 否则添加新标签并激活
          return {
            tabs: [...state.tabs, newTab],
            activeTabId: tabId,
          }
        })

        return tabId
      },

      closeTab: id => {
        const { tabs, activeTabId } = get()
        const tabIndex = tabs.findIndex(tab => tab.id === id)

        // 如果标签不存在，直接返回
        if (tabIndex === -1) return

        // 如果关闭的是当前激活的标签，则需要激活其他标签
        let newActiveTabId = activeTabId
        if (activeTabId === id) {
          // 优先激活右侧标签，如果没有则激活左侧标签
          const newActiveIndex = tabIndex === tabs.length - 1
            ? Math.max(0, tabIndex - 1)
            : tabIndex + 1

          newActiveTabId = tabs.length > 1 ? tabs[newActiveIndex].id : null
        }

        set({
          tabs: tabs.filter(tab => tab.id !== id),
          activeTabId: newActiveTabId,
        })
      },

      setActiveTab: id => {
        set({ activeTabId: id })
      },

      updateTab: (id, updates) => {
        set(state => ({
          tabs: state.tabs.map(tab =>
            tab.id === id ? { ...tab, ...updates } : tab,
          ),
        }))
      },

      closeOtherTabs: id => {
        const { tabs } = get()
        const targetTab = tabs.find(tab => tab.id === id)

        if (targetTab) {
          set({
            tabs: [targetTab],
            activeTabId: id,
          })
        }
      },

      closeAllTabs: () => {
        set({
          tabs: [],
          activeTabId: null,
        })
      },

      closeTabsToRight: id => {
        const { tabs } = get()
        const tabIndex = tabs.findIndex(tab => tab.id === id)

        if (tabIndex !== -1) {
          const newTabs = tabs.slice(0, tabIndex + 1)

          set(state => ({
            tabs: newTabs,
            activeTabId: state.activeTabId && newTabs.some(tab => tab.id === state.activeTabId)
              ? state.activeTabId
              : id,
          }))
        }
      },
    }),
    {
      name: 'VirtualTabsStore',
      partialize: state => {
        return pick(state, 'tabs', 'activeTabId')
      },
    }
  )
)

export default useVirtualTabsStore
