import React, { forwardRef, useImperativeHandle, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Copy, Download, X } from 'lucide-react'
import { MatrixModule } from '@/libs/request/api/matrix'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { toast } from 'react-toastify'

// 组件 ref 接口
export interface AuthorizationDialogRef {
  open: () => void
  close: () => void
}

// 平台配置
const platforms = [
  {
    id: 'windows',
    name: 'Windows10',
    icon: '⊞', // Windows logo placeholder
    description: '授权教程',
    downloadText: '立即安装',
    copyText: '复制账号授权码'
  },
  {
    id: 'macos-intel',
    name: 'MacOS Intel芯片',
    icon: '🍎', // Apple logo placeholder
    description: '授权教程',
    downloadText: '立即安装',
    copyText: '复制账号授权码'
  },
  {
    id: 'macos-m1',
    name: 'MacOS M系列芯片',
    icon: '🍎', // Apple M1 logo placeholder
    description: '授权教程',
    downloadText: '立即安装',
    copyText: '复制账号授权码'
  }
]

export const AuthorizationDialog = forwardRef<AuthorizationDialogRef, any>(
  function AuthorizationDialog(_props, ref) {
    const [open, setOpen] = useState(false)

    useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true)
      },
      close: () => {
        setOpen(false)
      }
    }), [])

    // 获取授权码
    const { data: authCode, isLoading, isError, refetch } = useQuery({
      queryKey: [QUERY_KEYS.AUTH_CODE],
      queryFn: () => MatrixModule.dyAccount.getCode(),
      enabled: open,

    })

    // 复制授权码
    const handleCopyCode = async () => {
      if (!authCode) {
        toast.error('错误:授权码获取失败，请重试')
        return
      }

      try {
        await navigator.clipboard.writeText(authCode)
        toast.success('成功: 授权码已复制到剪贴板')
      } catch (error) {
        // 降级方案：使用传统方法复制
        const textArea = document.createElement('textarea')
        textArea.value = authCode
        document.body.appendChild(textArea)
        textArea.select()
        try {
          document.execCommand('copy')
          toast.error('成功:授权码已复制到剪贴板')
        } catch (fallbackError) {
          toast.error('错误:复制失败，请手动复制授权码')
        }
        document.body.removeChild(textArea)
      }
    }

    // 重新获取授权码
    const handleRefreshCode = () => {
      refetch()
    }

    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl">智能分发授权</DialogTitle>
                <DialogDescription className="mt-1">
                  选择您的操作系统，下载客户端并使用授权码完成授权
                </DialogDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          <div className="space-y-6">
            {/* 授权码显示区域 */}
            <div className="bg-neutral-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-muted mb-1">授权码</div>
                  <div className="flex items-center space-x-2">
                    {isLoading ? (
                      <div>正在获取授权码...</div>
                    ) : isError ? (
                      <div >获取授权码失败</div>
                    ) : (
                      <code className=" px-3 py-1 rounded border text-sm font-mono">
                        {authCode || '暂无授权码'}
                      </code>
                    )}
                  </div>
                </div>
                <div className="flex space-x-2">
                  {isError && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRefreshCode}
                    >
                      重新获取
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyCode}
                    disabled={!authCode || isLoading}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    复制授权码
                  </Button>
                </div>
              </div>
            </div>

            {/* 平台选择区域 */}
            <div className="grid grid-cols-3 gap-4">
              {platforms.map(platform => (
                <div
                  key={platform.id}
                  className="border rounded-lg p-6 text-center space-y-4 hover:border-blue-300 transition-colors"
                >
                  {/* 平台图标 */}
                  <div className="flex justify-center">
                    <div className="w-16 h-16 bg-neutral-800 rounded-lg flex items-center justify-center text-2xl">
                      {platform.icon}
                    </div>
                  </div>

                  {/* 平台名称 */}
                  <div>
                    <h3 className="font-medium text-muted">{platform.name}</h3>
                    <p className="text-sm text-gray-500 mt-1">{platform.description}</p>
                  </div>

                  {/* 操作按钮 */}
                  <div className="space-y-2">
                    <Button
                      className="w-full"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {platform.downloadText}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full text-blue-400 hover:text-blue-500"
                      onClick={handleCopyCode}
                      disabled={!authCode || isLoading}
                    >
                      {platform.copyText}
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* 说明文字 */}
            <div className="text-sm text-gray-500 text-center">
              <p>1. 选择对应的操作系统下载客户端</p>
              <p>2. 安装完成后使用上方授权码完成账号授权</p>
              <p>3. 授权成功后即可开始使用智能分发功能</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }
)
