import {
  MaterialFile
} from '../../database.types.js'
import { CrudableIpcClient } from '../../crudable-ipc-client.js'

/**
 * 素材文件IPC客户端接口
 */
export interface MaterialFileIPCClient extends CrudableIpcClient<
  MaterialFile.IMaterialFile,
  MaterialFile.CreateParams,
  MaterialFile.QueryParams,
  MaterialFile.UpdateParams,
  MaterialFile.StatsResult
> {

  /**
   * 根据哈希查找素材
   * @param hash 文件哈希
   * @returns 素材文件或null
   */
  findByHash(hash: string): Promise<MaterialFile.IMaterialFile | null>

  /**
   * 查找用户素材
   * @param props.uid 用户ID
   * @param props.folderId 文件夹ID
   * @param props.teamId 团队ID
   * @returns 素材文件列表
   */
  userMaterials(props: { uid: string, folderId?: number, teamId?: number | null }): Promise<MaterialFile.IMaterialFile[]>

  /**
   * 根据类型查找素材
   * @param props.uid 用户ID
   * @param props.materialType 素材类型
   * @param props.teamId 团队ID
   * @returns 素材文件列表
   */
  byType(props: { uid: string, materialType: number, teamId?: number | null }): Promise<MaterialFile.IMaterialFile[]>

  /**
   * 搜索素材
   * @param props.keyword 关键词
   * @param props.uid 用户ID
   * @param props.teamId 团队ID
   * @returns 素材文件列表
   */
  search(props: { keyword: string, uid: string, teamId?: number | null }): Promise<MaterialFile.IMaterialFile[]>

  /**
   * 更新素材状态
   * @param props.id 素材文件ID
   * @param props.status 状态
   * @param props.reason 原因
   * @returns 是否更新成功
   */
  updateStatus(props: { id: string, status: number, reason?: string }): Promise<boolean>

  /**
   * 批量移动素材
   * @param props.ids 素材文件ID数组
   * @param props.folderId 目标文件夹ID
   * @returns 移动的记录数
   */
  batchMove(props: { ids: string[], folderId: number }): Promise<number>
}
