import React from 'react'
import { StickerOverlay } from '@clipnest/remotion-shared/types'

import { ImageStylePanel } from './image-style-panel'
import { ImageSettingsPanel } from './image-settings-panel'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { SettingsTabs, TabItem } from '../../../shared/settings-tabs'

export const ImageSetting: React.FC = () => {
  const { localOverlay: stickerOverlay } = useOverlayEditing<StickerOverlay>()

  const tabs: TabItem[] = [
    {
      value: 'screen',
      label: '画面',
      content: (
        <ImageStylePanel  />
      )
    },
    {
      value: 'adjust',
      label: '调节',
      content: (
        <ImageSettingsPanel />
      )
    },
  ]

  return (
    <div className="space-y-4">
      {/* Preview */}
      <div className="relative aspect-[16/7] w-full overflow-hidden rounded-sm border border-border bg-muted/40">
        <img
          src={stickerOverlay.src}
          alt="Image preview"
          className="h-full w-full object-cover"
        />
      </div>

      <SettingsTabs tabs={tabs} defaultTab="screen" />

    </div>
  )
}
