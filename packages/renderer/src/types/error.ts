/**
 * 错误类型枚举
 */
export enum ErrorType {
  // 网络错误
  NETWORK = 'network',
  // HTTP 错误
  HTTP = 'http',
  // 业务错误
  BUSINESS = 'business',
  // 验证错误
  VALIDATION = 'validation',
  // 权限错误
  PERMISSION = 'permission',
  // 资源错误
  RESOURCE = 'resource',
  // 运行时错误
  RUNTIME = 'runtime',
  // 未知错误
  UNKNOWN = 'unknown',
  // IPC 错误
  IPC = 'ipc',
}

/**
 * 错误严重程度枚举
 */
export enum ErrorSeverity {
  // 致命错误
  FATAL = 'fatal',
  // 错误
  ERROR = 'error',
  // 警告
  WARNING = 'warning',
  // 信息
  INFO = 'info',
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  // 错误消息
  message: string;
  // 错误代码
  code: string | number;
  // 错误类型
  type: ErrorType;
  // 错误严重程度
  severity: ErrorSeverity;
  // 错误时间
  timestamp: number;
  // 错误上下文
  context?: Record<string, any>;
  // 原始错误
  originalError?: unknown;
  // 错误堆栈
  stack?: string;
}

/**
 * 错误处理选项
 */
export interface ErrorHandlerOptions {
  // 是否显示 Toast 提示
  showToast?: boolean;
  // 是否上报错误
  report?: boolean;
  // 错误严重程度
  severity?: ErrorSeverity;
  // 错误上下文
  context?: Record<string, any>;
  // 自定义错误消息
  customMessage?: string;
}

/**
 * 错误上报数据接口
 */
export interface ErrorReportData {
  // 错误信息
  error: ErrorInfo;
  // 应用信息
  app: {
    version: string;
    environment: string;
  };
  // 用户信息
  user?: {
    id?: string;
    username?: string;
  };
  // 设备信息
  device?: {
    os: string;
    browser: string;
  };
  // 会话信息
  session?: {
    id?: string;
    startTime: number;
  };
} 