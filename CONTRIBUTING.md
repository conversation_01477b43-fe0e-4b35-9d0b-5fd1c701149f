# Contributing

First and foremost, thank you! We appreciate that you want to contribute to vite-electron-builder.

## Issues
- All reports of bugs, problems, and flaws are welcome.
- Improvements of documentation are welcome as well.
- If you have a question or a request for help, please prefer to create it in the discussion section.

## Pull Requests
- Developing this template is no different from using it to develop an end application. Just follow the [README.md](README.md) for instructions to set up. 
  It also describes the structure of the template and the available commands.
- Save your time. Before proposing any changes to the main repository, create an issue for discussion a proposal.
- When creating this template, I try to stick to minimalism. Please prefer to use the native features of the platform and avoid adding third-party dependencies without a really good reason.
- I follow the idea that code style should be left to the end template-user, so this repo does not contain any linters or formatters by default.
  I also don't make any requirements for strict adherence to the code style.
  That's up to you. Try to keep things consistent and don't go crazy.
- This repo has configured end-to-end tests. If you can't run tests locally - make a PR in draft.
