import React, { useCallback } from 'react'
import { SliderWithInput } from '@/components/ui/slider-with-input'
import { NumberInput } from '@/components/ui/number-input'

export type VideoFormValues = {
  zoom: number
  xPosition: number
  yPosition: number
  rotate: number
  opacity: number
  volume: number
  fadeIn: number
  fadeOut: number
  audioGain: boolean
  audioNoiseReduction: boolean
  speed: number
  duration: number
  trimStart: number
  trimEnd: number
}

type SectionTitleProps = {
  title: string
  onReset?: () => void
}

export const SectionTitle: React.FC<SectionTitleProps> = ({ title, onReset }) => (
  <div className="flex items-center justify-between">
    <span className="text-sm text-gray-700 dark:text-gray-300">{title}</span>
    {onReset && (
      <button
        type="button"
        onClick={onReset}
        className="text-xs text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300"
      >
        恢复全部
      </button>
    )}
  </div>
)

type FormSliderProps = {
  label?: string
  showInput?: boolean
  suffix?: string
  min?: number
  max?: number
  step?: number
  onChange?: (value: number, commit?: boolean) => void
  value: number
}

export const FormSlider: React.FC<FormSliderProps> = ({
  label,
  showInput = true,
  suffix,
  min,
  max,
  step,
  onChange,
  value
}) => (
  <div className="space-y-2 w-full">
    {label && <span className="text-xs text-gray-700 dark:text-gray-300">{label}</span>}
    <div className="flex items-center gap-2">
      {suffix && (
        <span className="text-xs text-gray-600 dark:text-gray-400">{suffix}</span>
      )}
      <SliderWithInput
        value={value}
        onChange={val => val?.toString() && onChange && onChange(val, false)}
        onChangeCommit={val => val?.toString() && onChange && onChange(val, true)}
        showInput={showInput}
        min={min}
        max={max}
        step={step}
        className="w-full flex-1 flex"
      />
    </div>
  </div>
)

type FormNumberInputProps = {
  label?: string
  className?: string
  suffix?: string
  value: number | string
  min?: number
  max?: number
  step?: number
  decimalPlaces?: number
  formatDisplay?: boolean

  onChange: (value: number) => void
  onBlur?: VoidFunction
}

export const FormNumberInput: React.FC<FormNumberInputProps> = ({
  label,
  suffix,
  value,
  min,
  max,
  step,
  onChange,
  onBlur,
  className = 'max-w-20',
  decimalPlaces = 2,
  formatDisplay = true
}) => {
  const parseInputValue = (val: string | number): number => {
    if (typeof val === 'string') {
      if (val === '' || val === '-') return 0
      const num = parseFloat(val)
      return isNaN(num) ? 0 : num
    }
    return val
  }

  const formatValue = (val: number | string): number => {
    const numValue = parseInputValue(val)
    if (!formatDisplay) return numValue

    if (Number.isInteger(numValue)) return numValue

    const factor = Math.pow(10, decimalPlaces)
    return Math.round(numValue * factor) / factor
  }

  const handleValueChange = useCallback((val: number) => {
    if (val !== undefined) {
      if (min !== undefined && val < min) {
        val = min
      }
      if (max !== undefined && val > max) {
        val = max
      }

      const formattedValue = formatValue(val)
      onChange(formattedValue)
    }
  }, [min, max])

  const displayValue = formatDisplay ? formatValue(value) : parseInputValue(value)

  return (
    <div className="flex items-center space-x-1">
      {label && <span className="text-xs text-gray-600 dark:text-gray-400">{label}</span>}
      <NumberInput
        value={displayValue}
        onChange={handleValueChange}
        onBlur={onBlur}
        className={className}
        min={min}
        max={max}
        step={step}
      />
      {suffix && <span className="text-xs text-gray-600 dark:text-gray-400">{suffix}</span>}
    </div>
  )
}
