import React, { useRef, useState } from 'react'
import { X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import request from '@/libs/request/request'
import { SimpleVideo } from '../publish/components/VideoSelectionSection'

interface VideoPreviewPopoverProps {
  video: SimpleVideo| null
  open?: boolean
  onOpenChange?: (open: boolean) => void
  trigger: React.ReactNode
}

export const VideoPreviewPopover: React.FC<VideoPreviewPopoverProps> = ({
  video,
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange,
  trigger
}) => {
  const [internalOpen, setInternalOpen] = useState(false)

  const isControlled = controlledOpen !== undefined && controlledOnOpenChange !== undefined
  const open = isControlled ? controlledOpen : internalOpen
  const onOpenChange = isControlled ? controlledOnOpenChange : setInternalOpen

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        {trigger}
      </PopoverTrigger>
      <PopoverContent
        className="w-[200px] aspect-[9/16] p-0 border-0 bg-background/90 shadow-2xl"
        side="right"
        align="center"
        sideOffset={10}
      >
        <div className="relative  rounded-lg overflow-hidden h-full w-full">
          {/* 关闭按钮 */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-1 right-1 z-10 text-white hover:bg-white/20 h-8 w-8 p-0"
            onClick={() => onOpenChange(false)}
          >
            <X className="w-4 h-4" />
          </Button>

          {/* 视频标题 */}
          <div className="absolute top-2 left-2 z-10  max-w-[calc(100%-4rem)]">
            <h3 className="text-sm font-medium truncate">{video?.name || '视频预览'}</h3>
          </div>

          {/* 视频播放器 */}
          <div className="relative pt-8 h-full w-full">
            <VideoContent video={video} />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

const VideoContent: React.FC<{ video: Partial<SimpleVideo> | null }> = ({ video }) => {
  const videoRef = useRef<HTMLVideoElement>(null)

  const { data: videoUrl } = useQuery({
    queryKey: [QUERY_KEYS.VIDEO_LINK, video],
    queryFn: () => request.get(video!.url!),
    enabled: !!video?.url
  })

  if (!video) {
    return (
      <div className="w-full h-48 flex items-center justify-center text-white text-sm">
        无视频数据
      </div>
    )
  }

  if (!videoUrl) {
    return (
      <div className="w-full h-48 flex items-center justify-center text-white text-sm">
        加载中...
      </div>
    )
  }

  return (
    <video
      ref={videoRef}
      src={videoUrl}
      poster={video.cover}
      className="w-full h-full object-contain"
      controls
      playsInline
      autoPlay
    />
  )
}
