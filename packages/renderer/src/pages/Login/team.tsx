import { randomGradient3 } from '@/components/fake-image'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { useQueryTeamList } from '@/hooks/queries/useQueryTeam'
import { TeamAPI } from '@/libs/request/api/team'
import { TeamManager } from '@/libs/storage'
import { useQueryClient } from '@tanstack/react-query'
import { ChevronRight, Link, Users2 } from 'lucide-react'
import React, { useState } from 'react'
import { useNavigate } from 'react-router'
import { toast } from 'react-toastify'

export default function TeamInit() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { data: teamList, isLoading } = useQueryTeamList()

  const [showForm, setShowForm] = useState(false)
  const [tab, setTab] = useState<'create' | 'join'>('create')

  const [name, setName] = useState('')
  const [nameError, setNameError] = useState('')
  const [inviteCode, setInviteCode] = useState('')
  const [inviteCodeError, setInviteCodeError] = useState('')

  async function check(teamId: number) {
    try {
      return await TeamAPI.check({ teamId })
    } catch (error: any) {
      return false
    }
  }

  async function handleSelectTeam(teamId: number) {
    if (await check(teamId)) {
      TeamManager.switch(teamId)
      navigate('/', { replace: true })
    } else {
      toast.error('团队不存在或已被删除')
      await queryClient.resetQueries()
    }
  }

  async function handleCreateTeam() {
    const errorMapping = (msg?: string) => {
      if (!msg) return '创建团队失败'
      if (msg.endsWith('已存在')) return '团队名称已存在，请更换名称后重试'
      return msg
    }

    try {
      if (!name.trim()) throw new Error('团队名称不能为空')
      const teamId = await TeamAPI.create({ name })
      TeamManager.switch(teamId)
      await queryClient.resetQueries()
      navigate('/', { replace: true })
    } catch (error: any) {
      setNameError(errorMapping(error?.message))
    }
  }

  async function handleJoinTeam() {
    try {
      await TeamAPI.join({ inviteCode })
      await queryClient.resetQueries()
      navigate('/', { replace: true })
    } catch (error: any) {
      setInviteCodeError(error?.message || '加入团队失败')
    }
  }

  return (
    <div className="absolute inset-0 flex flex-col items-stretch justify-start z-10 px-30 py-37">
      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-blue-500" />
        </div>
      ) : teamList?.length && !showForm ? (
        <div className="flex flex-col flex-1 overflow-auto">
          <div className="flex justify-between items-end">
            <h2 className="text-4xl">选择团队</h2>
            <button
              className="text-blue-500 hover:text-blue-500/90 text-sm cursor-pointer"
              onClick={() => setShowForm(true)}
            >
              创建或加入团队
            </button>
          </div>
          <div className="flex flex-col gap-3 overflow-auto mt-6 rounded-[1.75rem]">
            {teamList.map(team => (
              <button
                key={team.id}
                className="bg-[hsla(0,0%,96%,0.05)] rounded-full h-14 flex items-center cursor-pointer flex-none"
                onClick={() => handleSelectTeam(team.id)}
              >
                <Avatar className="ml-2">
                  <AvatarFallback style={{ backgroundImage: randomGradient3(team.id) }}>
                    {team.name.slice(0, 1).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span className="text-lg font-semibold ml-2 truncate">{team.name}</span>
                <ChevronRight className="ml-auto mr-4 size-6" />
              </button>
            ))}
          </div>
        </div>
      ) : (
        <>
          <div className="text-4xl flex justify-between *:text-muted-foreground *:aria-selected:text-inherit *:cursor-pointer *:aria-selected:cursor-auto">
            <button aria-selected={tab === 'create'} onClick={() => setTab('create')}>
              创建团队
            </button>
            <button aria-selected={tab === 'join'} onClick={() => setTab('join')}>
              加入团队
            </button>
          </div>
          <div className="flex flex-col mt-15 gap-6">
            <p className="text-muted-foreground">
              {tab === 'create'
                ? '创建团队后，您可以邀请其他成员加入。'
                : '输入团队邀请码加入团队，请联系团队管理员获取。'}
            </p>
            {tab === 'create' ? (
              <div className="flex-1 flex relative">
                <input
                  className="bg-[hsla(0,0%,96%,0.05)] rounded-full flex-1 pl-13 h-14"
                  placeholder="请输入团队名称"
                  value={name}
                  onChange={e => {
                    setName(e.currentTarget.value.slice(0, 20))
                  }}
                />
                <span className="absolute right-4 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                  {name.length}/20
                </span>
                <Users2 className="absolute left-4 top-1/2 -translate-y-1/2 size-6" />
                {nameError && (
                  <p className="absolute left-5 bottom-0 translate-y-1/1 text-destructive text-sm">{nameError}</p>
                )}
              </div>
            ) : (
              <div className="flex-1 flex relative">
                <input
                  required
                  className="bg-[hsla(0,0%,96%,0.05)] rounded-full flex-1 pl-13 h-14 valid:font-mono"
                  placeholder="请输入邀请码"
                  value={inviteCode}
                  onChange={e => setInviteCode(e.currentTarget.value.slice(0, 32))}
                />
                <span className="absolute right-4 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                  {inviteCode.length}/32
                </span>
                <Link className="absolute left-4 top-1/2 -translate-y-1/2 size-6" />
                {inviteCodeError && (
                  <p className="absolute left-5 bottom-0 translate-y-1/1 text-destructive text-sm">{inviteCodeError}</p>
                )}
              </div>
            )}
          </div>
          <div className="mt-auto flex relative">
            <div className="absolute -inset-3 rounded-full blur-xs bg-linear-[91.55deg] from-[rgba(0,246,254,0.6)] to-[rgba(255,106,0,0.6)]" />
            <button
              className="flex-1 h-16 rounded-full bg-black z-10 cursor-pointer"
              onClick={() => {
                if (tab === 'create') handleCreateTeam()
                if (tab === 'join') handleJoinTeam()
              }}
            >
              {tab === 'create' ? '创建团队' : '加入团队'}
            </button>
            {!!teamList?.length && (
              <button
                className="absolute text-blue-500 hover:text-blue-500/90 text-sm cursor-pointer bottom-0 translate-y-3/2 right-8"
                onClick={() => setShowForm(false)}
              >
                选择现有团队
              </button>
            )}
          </div>
        </>
      )}
    </div>
  )
}
