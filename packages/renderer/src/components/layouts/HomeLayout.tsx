import React from 'react'
import { Ban, Grid2X2, Home, Users2 } from 'lucide-react'
import { BaseLayout } from './BaseLayout'

export interface MenuItem {
  key: string
  title: string
  icon: React.ReactNode
  path: string
}

export const HomeLayout: React.FC = () => {
  // 菜单项定义
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      title: '工作台',
      icon: <Home size={16} />,
      path: '/home/<USER>',
    },
    {
      key: 'projects',
      title: '项目管理',
      icon: <Home size={16} />,
      path: '/home/<USER>',
    },
    {
      key: 'matrix',
      title: '矩阵宝',
      icon: <Grid2X2 size={16} />,
      path: '/home/<USER>',
    },
    {
      key: 'teams',
      title: '我的团队',
      icon: <Users2 size={16} />,
      path: '/home/<USER>',
    },
    {
      key: 'error-demo',
      title: '报错DEMO',
      icon: <Ban size={16} />,
      path: '/home/<USER>',
    },
  ]

  return <BaseLayout menuItems={menuItems} />
}
