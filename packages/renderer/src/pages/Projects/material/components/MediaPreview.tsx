import React, { useRef, useState, useCallback } from 'react'
import { rawInstance } from '@/libs/request/instance'
import { MaterialResource } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import { useResource } from '@/modules/video-editor/hooks/resource/useResource'
import { AuthedImg } from '@/components/authed-img'

// 去掉签名参数，仅用于缓存 key
const getUnsignedUrlKey = (url: string): string => {
  try {
    const u = new URL(url)
    return `${u.origin}${u.pathname}`
  } catch {
    return url
  }
}

interface MediaPreviewProps {
  media: MaterialResource.Media
  isEditItem: boolean
  orientation: string
}

const MediaPreview: React.FC<MediaPreviewProps> = ({ media }) => {
  const { preloadImage } = useResource()

  const [hoverFrame, setHoverFrame] = useState(0)
  const [frameWidth, setFrameWidth] = useState<number>(200)
  const [frameHeight, setFrameHeight] = useState<number>(200)
  const [frameCount, setFrameCount] = useState<number>(0)
  const [naturalWidth, setNaturalWidth] = useState<number>(0)
  const [naturalHeight, setNaturalHeight] = useState<number>(0)
  const [coverFrameUrl, setCoverFrameUrl] = useState<string | null>(null)

  const containerRef = useRef<HTMLDivElement | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  // 请求瓦片图并解析尺寸
  const fetchCoverFrame = async (retry = true) => {
    try {
      const aTag = await rawInstance.get(media.coverFrame!).then(r => r.data)
      const doc = new DOMParser().parseFromString(aTag, 'text/html')
      const href = decodeURIComponent(doc.querySelector('a')?.href || '')
      if (!href) return
      setCoverFrameUrl(href)

      const regex = /_(\d+)x(\d+)_(\d+)_/
      const match = href.match(regex)
      if (!match) return

      const img = await preloadImage(getUnsignedUrlKey(href)).catch(async error => {
        if (retry) {
          console.warn('签名可能失效，尝试重新获取 signed URL')
          await fetchCoverFrame(false) // 只重试一次
        } else {
          console.error('图片加载失败，已放弃重试:', error)
        }
        return null
      })

      if (!img) return

      img.src = href
      img.onload = () => {
        if (!containerRef.current) return
        const { width: containerWidth, height: containerHeight } = containerRef.current.getBoundingClientRect()

        const framesPerRow = 10 // 固定每行10帧
        const rawFrameWidth = parseInt(match[1], 10) // 原图一帧的宽度
        const rawFrameHeight = parseInt(match[2], 10) // 原图一帧的高度
        const count = parseInt(match[3], 10) // 帧数
        const rows = Math.ceil(count / framesPerRow) // 行数

        const isVertical = rawFrameHeight > rawFrameWidth
        let displayFrameWidth: number
        let displayFrameHeight: number

        if (isVertical) {
          // 竖屏：固定高度，宽度按比例缩放
          displayFrameHeight = containerHeight
          displayFrameWidth = displayFrameHeight * (rawFrameWidth / rawFrameHeight)
        } else {
          // 横屏：固定宽度，高度按比例缩放
          displayFrameWidth = containerWidth
          displayFrameHeight = displayFrameWidth * (rawFrameHeight / rawFrameWidth)
        }

        const naturalWidths = displayFrameWidth * framesPerRow
        const naturalHeights = displayFrameHeight * rows

        setNaturalWidth(naturalWidths)
        setNaturalHeight(naturalHeights)
        // console.log('背景图总宽度', naturalWidth, '背景图总高度', naturalHeight)

        // 一帧显示的宽度和高度
        setFrameWidth(displayFrameWidth)
        setFrameHeight(displayFrameHeight)

        // Ensure frameCount is updated correctly
        setFrameCount(count)

        // console.log(`瓦片图加载成功, 总帧数: ${count}, 每帧自适应宽度: ${frameWidth}, 每帧自适应高度: ${frameHeight}`)
      }
    } catch (err) {
      console.error('获取瓦片图失败:', err)
    }
  }

  // 鼠标移动时计算当前帧
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!containerRef.current || !coverFrameUrl || frameCount === 0) return

    // 获取容器的位置信息
    const { left, width } = containerRef.current.getBoundingClientRect()
    const relativeX = e.clientX - left

    // 计算相对位置的比例
    const proportion = relativeX / width
    const frameIndex = Math.max(0, Math.min(Math.floor(proportion * frameCount), frameCount - 1))
    // console.log(`相对位置: ${relativeX}, 容器宽度: ${width}, 总帧数: ${frameCount}, 当前帧索引: ${frameIndex}`)

    if (frameIndex !== hoverFrame) {
      setHoverFrame(frameIndex)
    }
  }

  // 鼠标进入容器时加载瓦片图
  const handleMouseEnter = useCallback(() => {
    // 如果已经有定时器在运行，先清除掉
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      if (!coverFrameUrl) {
        fetchCoverFrame()
      }
    }, 300) // 延迟 300 毫秒加载
  }, [coverFrameUrl])

  // 鼠标离开容器时重置帧索引
  const handleMouseLeave = () => {
    setHoverFrame(0)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current) // 清除延迟加载的定时器
    }
  }
  return (
    <div className={cn('flex relative group h-full w-full items-center justify-center')} ref={containerRef}>
      <div
        className="relative overflow-hidden rounded cursor-pointer "
        style={{ height: frameHeight, width: frameWidth }} // 设置容器的高度
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseMove={handleMouseMove}
      >
        {coverFrameUrl ? (
          (() => {
            const framesPerRow = 10 // 每行10帧
            const row = Math.floor(hoverFrame / framesPerRow) // 计算当前帧的行号
            const col = hoverFrame % framesPerRow // 计算当前帧的列号

            return (
              <div
                className="w-full h-full bg-no-repeat"
                style={{
                  width: `${naturalWidth}px`,
                  height: `${naturalHeight}px`,
                  backgroundImage: `url(${coverFrameUrl})`,
                  backgroundSize: '100% 100%',
                  backgroundPosition: `-${col * frameWidth}px -${row * frameHeight}px`, // 根据行列计算背景偏移量
                }}
              />
            )
          })()
        ) : (
          <AuthedImg src={media.cover} alt={media.fileName} className="w-full h-full" />
        )}
      </div>
    </div>
  )
}

export default MediaPreview
