import {
  CaptionOverlay,
  Overlay,
  OverlayType,
  RenderableOverlay,
  SoundOverlay,
  StickerOverlay,
  StoryboardOverlay,
  TextOverlay,
  VideoOverlay
} from '@clipnest/remotion-shared/types'
import React, { useContext } from 'react'

type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type OverlayUpdater<T> = DeepPartial<T> | ((overlay: T) => T)

export type GetTypedOverlay<T extends OverlayType> = T extends OverlayType.TEXT
  ? TextOverlay
  : T extends OverlayType.VIDEO
    ? VideoOverlay
    : T extends OverlayType.SOUND
      ? SoundOverlay
      : T extends OverlayType.CAPTION
        ? CaptionOverlay
        : T extends OverlayType.STICKER
          ? StickerOverlay
          : T extends OverlayType.STORYBOARD
            ? StoryboardOverlay
            : never

export type CachedOverlaysContextValues = {
  overlays: RenderableOverlay[],

  requestUpdate<TOverlayType extends OverlayType>(
    id: Overlay['id'],
    overlay: OverlayUpdater<GetTypedOverlay<TOverlayType>>,
    commit?: boolean
  ): void
}

export const CachedOverlaysContext = React.createContext<CachedOverlaysContextValues>(null as any)

export const useCachedOverlaysContext = () => useContext(CachedOverlaysContext)
