.login-box {
  border-image-source: linear-gradient(
    137.32deg,
    rgba(253, 240, 254, 0) 2.6%,
    rgba(255, 255, 255, 0.2) 52.24%,
    rgba(151, 144, 152, 0) 100%
  );
}

.login-content {
  background: linear-gradient(136.91deg, #1c3136 3.18%, #0f1b2b 49.55%, #2e2119 94.08%);
}

.middle-1 {
  background: linear-gradient(130.66deg, #f3fffe 0%, rgba(103, 153, 149, 0) 42.77%);
  box-shadow: 4px 4px 4px 0px hsla(172, 100%, 58%, 0.25) inset;
  backdrop-filter: blur(4px);
  border-radius: 20px;
}

.outer-1 {
  background: linear-gradient(140.36deg, #f3fffc 0%, rgba(103, 153, 140, 0) 84.93%);
  backdrop-filter: blur(20px);
  border-radius: 20px;
}

.inner-1-1 {
  background: linear-gradient(138.23deg, #ffffff 1.45%, rgba(153, 153, 153, 0) 97.84%);
  box-shadow: -4px -4px 30px 20px hsla(175, 100%, 80%, 0.2);
  border-radius: 20px;
}

.inner-1-2 {
  background: linear-gradient(146.2deg, #2ccab6 0%, rgba(153, 153, 153, 0) 73.64%);
  box-shadow: -4px -4px 50px 60px hsla(176, 100%, 57%, 0.05);
  border-radius: 20px;
}

.middle-2 {
  background: linear-gradient(130.66deg, #fffcf3 0%, rgba(169, 169, 169, 0) 42.77%);
  box-shadow: -4px -4px 4px 0px hsla(27, 100%, 58%, 0.25) inset;
  backdrop-filter: blur(4px);
  border-radius: 20px;
}

.outer-2 {
  background: linear-gradient(136.37deg, #fff4e7 0%, rgba(153, 135, 103, 0) 59.53%);
  backdrop-filter: blur(20px);
  border-radius: 20px;
}

.inner-2-1 {
  background: linear-gradient(136.45deg, #ffffff 1.46%, rgba(153, 153, 153, 0) 49.42%);
  box-shadow: -4px -4px 30px 20px hsla(39, 100%, 80%, 0.2);
  border-radius: 20px;
}

.inner-2-2 {
  background: linear-gradient(132.45deg, #f6e4c9 0%, rgba(153, 153, 153, 0) 50.88%);
  box-shadow: -4px -4px 50px 60px hsla(35, 100%, 35%, 0.05);
  border-radius: 20px;
}
