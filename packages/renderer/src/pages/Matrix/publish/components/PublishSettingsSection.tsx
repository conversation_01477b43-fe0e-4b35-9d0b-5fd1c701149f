import React from 'react'
import { <PERSON>, Controller, FieldErrors } from 'react-hook-form'
import { Clock, Info } from 'lucide-react'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { MountType, PublishMode, Setting, TimeType } from '@/types/matrix/douyin'
import { toast } from 'react-toastify'

const mountTypeOptions = [
  { value: MountType.NONE, label: '不挂载' },
  { value: MountType.LOCATION, label: '位置' },
  { value: MountType.CART, label: '购物车' }
]

const publishModeOptions = [
  { value: PublishMode.CHECK_IN, label: '打卡' },
  { value: PublishMode.GOODS, label: '带货' }
]

const settingOptions = [
  { value: Setting.ONE_ACCOUNT_ONE_VIDEO, label: '一个账号一个视频' },
  { value: Setting.ONE_ACCOUNT_MULTIPLE_VIDEOS, label: '一个账号多个视频' }
]

const timeTypeOptions = [
  { value: TimeType.IMMEDIATE, label: '立即发布' },
  { value: TimeType.SCHEDULED, label: '定时发布' },
  { value: TimeType.LOOP, label: '循环定时发布' }
]

interface PublishSettingsSectionProps {
  control: Control<any>
  errors?: FieldErrors
  isCartDisabled?: boolean
}

export const PublishSettingsSection: React.FC<PublishSettingsSectionProps> = ({
  control,
  errors,
  isCartDisabled = false
}) => {
  // 处理购物车选项点击事件
  
  const handleCartOptionClick = (e: React.MouseEvent, isDisabled: boolean) => {
    if (isDisabled) {
      e.preventDefault()
      e.stopPropagation()
      toast('请先选择您的发布账号，完成选择后才能成功添加到购物车。', {
        type: 'warning'
      })
    }
  }
  return (
    <div className="space-y-6">
      {/* 发布模式 */}
      <div className="flex gap-6 items-center">
        <Label>发布模式</Label>
        <Controller
          name="publishMode"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value.toString()}
              onValueChange={(value: string) => field.onChange(Number(value))}
              className="flex gap-3"
            >
              {publishModeOptions.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.value.toString()}
                    id={`publishMode-${option.value}`}
                  />
                  <Label
                    htmlFor={`publishMode-${option.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}
        />
        {errors?.publishMode && (
          <p className="text-sm text-red-500">{(errors.publishMode as any).message}</p>
        )}
      </div>

      {/* 视频位置/小程序挂载 */}
      <div className="flex gap-6 items-center">
        <Label>视频位置/小程序挂载</Label>
        <Controller
          name="mountType"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value.toString()}
              onValueChange={(value: string) => {
                const numValue = Number(value)
                // 如果是购物车选项且被禁用，则不允许选择
                if (numValue === MountType.CART && isCartDisabled) {
                  handleCartOptionClick({} as React.MouseEvent, true)
                  return
                }
                field.onChange(numValue)
              }}
              className="flex gap-3"
            >
              {mountTypeOptions.map(option => {
                const isCartOption = option.value === MountType.CART
                const isDisabled = isCartOption && isCartDisabled

                return (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem
                      value={option.value.toString()}
                      id={`mountType-${option.value}`}
                      disabled={isDisabled}
                      className={isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
                    />
                    <Label
                      htmlFor={`mountType-${option.value}`}
                      className={`text-sm font-normal ${
                        isDisabled
                          ? 'opacity-50 cursor-not-allowed'
                          : 'cursor-pointer'
                      }`}
                      onClick={e => {
                        if (isDisabled) {
                          handleCartOptionClick(e, true)
                        }
                      }}
                    >
                      {option.label}
                    </Label>
                  </div>
                )
              })}
            </RadioGroup>
          )}
        />
        {errors?.mountType && (
          <p className="text-sm text-red-500">{(errors.mountType as any).message}</p>
        )}
      </div>

      {/* 发布设置 */}
      <div className="flex gap-6 items-center">
        <Label>发布设置</Label>
        <Controller
          name="setting"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value.toString()}
              onValueChange={(value: string) => field.onChange(Number(value))}
              className="flex gap-3"
            >
              {settingOptions.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.value.toString()}
                    id={`setting-${option.value}`}
                  />
                  <Label
                    htmlFor={`setting-${option.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}
        />
        {errors?.setting && (
          <p className="text-sm text-red-500">{(errors.setting as any).message}</p>
        )}
      </div>

      {/* 发布时间类型 */}
      <div className="flex gap-6 items-center">
        <Label className="flex items-center gap-2">
          <Clock className="w-4 h-4" />
          发布时间类型
        </Label>
        <Controller
          name="timeType"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value.toString()}
              onValueChange={(value: string) => field.onChange(Number(value))}
              className="flex gap-3"
            >
              {timeTypeOptions.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.value.toString()}
                    id={`timeType-${option.value}`}
                  />
                  <Label
                    htmlFor={`timeType-${option.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {option.label}
                  </Label>
                  {option.value === TimeType.LOOP && (
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="space-y-2">
                          <h4 className="font-medium">循环定时发布限制</h4>
                          <p className="text-sm text-muted-foreground">
                            • 最大支持14天内日期选择<br />
                            • 最多可选择14天<br />
                            • 超出范围的日期将无法选择
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
              ))}
            </RadioGroup>
          )}
        />
      </div>
    </div>
  )
}
