import React, { useRef, useState } from 'react'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'

interface TabBarProps {
  onAddTab?: () => void
  onHomeClick?: () => void
}

const TabBar: React.FC<TabBarProps> = () => {
  const {
    tabs, activeTabId,
    closeTab, setActiveTab, closeOtherTabs, closeTabsToRight, closeAllTabs, goToHomePage
  } = useVirtualTabsStore()

  const [showContextMenu, setShowContextMenu] = useState(false)
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 })
  const [contextMenuTabId, setContextMenuTabId] = useState<string | null>(null)
  const tabsRef = useRef<HTMLDivElement>(null)

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, tabId: string) => {
    e.preventDefault()
    setContextMenuPosition({ x: e.clientX, y: e.clientY })
    setContextMenuTabId(tabId)
    setShowContextMenu(true)
  }

  // 关闭右键菜单
  const closeContextMenu = () => {
    setShowContextMenu(false)
  }

  // 处理关闭其他标签
  const handleCloseOtherTabs = () => {
    if (contextMenuTabId) {
      closeOtherTabs(contextMenuTabId)
      closeContextMenu()
    }
  }

  // 处理关闭右侧标签
  const handleCloseTabsToRight = () => {
    if (contextMenuTabId) {
      closeTabsToRight(contextMenuTabId)
      closeContextMenu()
    }
  }

  // 处理关闭所有标签
  const handleCloseAllTabs = () => {
    closeAllTabs()
    closeContextMenu()
  }

  // 监听全局点击事件，关闭右键菜单
  React.useEffect(() => {
    const handleClickOutside = () => {
      if (showContextMenu) {
        closeContextMenu()
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [showContextMenu])

  return (
    <div className="flex flex-col w-full">
      {/* 标签栏 */}
      <div
        ref={tabsRef}
        className="flex items-center h-9 overflow-x-auto"
      >
        {/* 常驻Home图标 */}
        <div
          className="flex items-center justify-center px-3 h-full cursor-pointer app-no-drag"
          onClick={goToHomePage}
          title="主页"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-neutral-600 dark:text-neutral-400"
          >
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
            <polyline points="9 22 9 12 15 12 15 22" />
          </svg>
        </div>

        {tabs.map(tab => (
          <div
            key={tab.id}
            className={`
              flex items-center min-w-[140px] max-w-[200px] h-full px-3
              border-r border-neutral-200 dark:border-neutral-700
              cursor-pointer select-none transition-colors app-no-drag
                ${activeTabId === tab.id
            ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-white'
            : 'bg-transparent text-neutral-600 dark:text-neutral-400 hover:bg-neutral-50 dark:hover:bg-neutral-750'}
            `}
            onClick={() => setActiveTab(tab.id)}
            onContextMenu={e => handleContextMenu(e, tab.id)}
            onAuxClick={() => closeTab(tab.id)}
          >
            {/* 图标 */}
            {/*{tab.icon && <div className="mr-2">{tab.icon}</div>}*/}

            {/* 标题 */}
            <div className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
              {tab.title}
            </div>

            {/* 关闭按钮 */}
            {tab.closable !== false && (
              <button
                className="ml-2 w-5 h-5 flex items-center justify-center rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-600 transition-colors"
                onClick={e => {
                  e.stopPropagation()
                  closeTab(tab.id)
                }}
                aria-label="关闭标签"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            )}
          </div>
        ))}

      </div>

      {/* 右键菜单 */}
      {showContextMenu && (
        <div
          className="fixed z-50 bg-white dark:bg-neutral-800 shadow-lg rounded border border-neutral-200 dark:border-neutral-700 py-1 w-48"
          style={{
            left: `${contextMenuPosition.x}px`,
            top: `${contextMenuPosition.y}px`,
          }}
        >
          <button
            className="w-full text-left px-4 py-2 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors"
            onClick={handleCloseOtherTabs}
          >
            关闭其他标签
          </button>
          <button
            className="w-full text-left px-4 py-2 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors"
            onClick={handleCloseTabsToRight}
          >
            关闭右侧标签
          </button>
          <div className="border-t border-neutral-200 dark:border-neutral-700 my-1" />
          <button
            className="w-full text-left px-4 py-2 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors"
            onClick={handleCloseAllTabs}
          >
            关闭所有标签
          </button>
        </div>
      )}
    </div>
  )
}

export default TabBar
