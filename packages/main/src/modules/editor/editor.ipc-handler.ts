import { Inject, Injectable } from '@nestjs/common'
import { EditorService } from './editor.service.js'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'

@Injectable()
export class Editor<PERSON>pc<PERSON>andler extends BaseIPCHandler<'editor'> {

  protected readonly platformPrefix = 'editor'

  constructor(
    @Inject(EditorService) private readonly editorService: EditorService
  ) {
    super()
  }

  registerAll() {
    this.registerHandler('generateCombos', data => {
      return this.editorService.generateCombos(data)
    })

    this.registerHandler('extractVideoKeyFrames', data => {
      return this.editorService.extractVideoKeyFrames(data)
    })

    this.registerHandler('saveEditorState', data => {
      return this.editorService.saveEditorState(data)
    })

    this.registerHandler('loadEditorState', data => {
      return this.editorService.loadEditorState(data)
    })

    this.registerHandler('uploadMixcutResult', data => {
      return this.editorService.uploadMixcutResult(data)
    })

    this.registerHandler('getAudioDuration', data => {
      return this.editorService.getAudioDuration(data)
    })
  }
}
