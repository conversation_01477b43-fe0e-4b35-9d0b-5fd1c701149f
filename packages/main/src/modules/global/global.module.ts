import { Global, Module } from '@nestjs/common'
import { RequestService } from './request.service.js'
import { NestDatabaseService } from './database.service.js'
import { BaseInfoIPCHandler } from './base-info.ipc-handler.js'
import { AutoUpdaterIPCHandler } from './auto-updater.ipc-handler.js'
import { AutoUpdaterService } from './auto-updater.service.js'

@Module({
  providers: [
    BaseInfoIPCHandler,
    RequestService,
    AutoUpdaterService,
    AutoUpdaterIPCHandler,
    {
      provide: NestDatabaseService,
      useFactory: async () => {
        const dbService = new NestDatabaseService('clipnest.db')
        // 立即初始化数据库
        await dbService.init()
        return dbService
      }
    }
  ],
  exports: [
    RequestService,
    NestDatabaseService,
  ]
})
@Global()
export class GlobalModule {}
