import React from 'react'
import { StickerOverlay, OverlayType, VideoOverlay } from '@clipnest/remotion-shared/types'
import { ENABLE_VIDEO_KEYFRAME_EXTRACTING } from '../constants'
import { toAbsoluteUrl } from '@clipnest/remotion-shared/utils'
import { cacheManager } from '@/libs/cache/cache-manager'

interface UseKeyframesProps {
  /**
   * The video overlay object containing source and duration information
   */
  overlay: VideoOverlay | StickerOverlay
  baseUrl?: string
}

interface FrameInfo {
  frameNumber: number
  dataUrl: string
}

type KeyframesHook = {
  /**
   * Array of extracted frame data URLs
   */
  frames: string[]

  /**
   * Array of frame numbers to show in the timeline
   */
  previewFrames: number[]

  /**
   * Function to determine if a preview frame should be visible
   */
  // isFrameVisible: boolean

  /**
   * Boolean indicating whether frames are currently being extracted
   */
  isLoading: boolean
}

/**
 * A custom hook that extracts and manages keyframes from video overlays for timeline preview.
 * Uses an optimized approach combining browser capabilities with Remotion utilities.
 *
 * @description
 * This hook handles:
 * - Extracting preview frames from video overlays
 * - Calculating optimal number of keyframes based on container width and zoom level
 * - Managing frame visibility based on current timeline position
 * - Responsive updates when container size changes
 */
export const useKeyframes = ({
  overlay,
  baseUrl,
}: UseKeyframesProps): KeyframesHook => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [frames, setFrames] = React.useState<FrameInfo[]>([])
  const extractionTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)

  // Memoize stable overlay values
  const overlayMeta = React.useMemo(
    () => ({
      id: overlay.id,
      src: overlay.src,
      type: overlay.type,
      durationInFrames: overlay.durationInFrames,
    }),
    [overlay],
  )

  // Store previous overlay details
  const previousOverlayRef = React.useRef<{
    id: string | number
    src?: string
    durationInFrames?: number
  } | null>(null)

  // Memoize frame data transformations
  const frameData = React.useMemo(() => {
    return {
      dataUrls: frames.map(f => f.dataUrl),
      frameNumbers: frames.map(f => f.frameNumber),
    }
  }, [frames])

  // Cleanup function to release resources
  const cleanup = React.useCallback(() => {
    if (extractionTimeoutRef.current) {
      clearTimeout(extractionTimeoutRef.current)
      extractionTimeoutRef.current = null
    }
  }, [])

  // 使用后端 IPC 调用进行关键帧提取
  const performExtraction = React.useCallback(
    async () => {
      if (overlayMeta.type !== OverlayType.VIDEO || !overlayMeta.src) return

      // 检查是否需要重新提取帧
      const previousOverlay = previousOverlayRef.current
      const shouldReExtract = !previousOverlay
        || String(previousOverlay.id) !== String(overlayMeta.id)
        || previousOverlay.src !== overlayMeta.src
        || previousOverlay.durationInFrames !== overlayMeta.durationInFrames

      // 更新之前的 overlay 引用
      previousOverlayRef.current = {
        id: overlayMeta.id,
        src: overlayMeta.src,
        durationInFrames: overlayMeta.durationInFrames,
      }

      if (!shouldReExtract) return

      try {
        setIsLoading(true)
        setFrames([]) // 重置帧数据

        // 首先检查缓存
        const videoSrc = overlayMeta.src
        const cachedFrames = await cacheManager.keyframe.getKeyframes(videoSrc)
        if (
          cachedFrames
            && cachedFrames.frames
            && cachedFrames.frames.length > 0
            && cachedFrames.frames.every(frame => frame?.startsWith('data:image'))
            && cachedFrames.durationInFrames === overlayMeta.durationInFrames
            && Date.now() - cachedFrames.lastUpdated < 300000 // 5分钟缓存有效期
        ) {
          setFrames(
            cachedFrames.previewFrames.map((frameNumber, index) => ({
              frameNumber,
              dataUrl: cachedFrames.frames[index],
            })),
          )
          console.debug('[useKeyframes] 使用缓存的关键帧')
          return
        }

        // 处理视频源 URL，与 video-layer-content 保持一致
        let processedVideoSrc = overlayMeta.src

        // 如果是相对 URL 且提供了 baseUrl
        if (overlayMeta.src.startsWith('/') && baseUrl) {
          processedVideoSrc = `${baseUrl}${overlayMeta.src}`
        }
        // 否则使用 toAbsoluteUrl 辅助函数处理相对 URL
        else if (overlayMeta.src.startsWith('/')) {
          processedVideoSrc = toAbsoluteUrl(overlayMeta.src)
        }

        // 调用后端 IPC 方法提取关键帧
        const extractedFrames = await window.editor.extractVideoKeyFrames({
          src: processedVideoSrc
        })

        // 更新状态
        setFrames(extractedFrames)
        setIsLoading(false)

        // 缓存提取的关键帧
        if (extractedFrames.length > 0) {
          void cacheManager.keyframe.setKeyframes(videoSrc, {
            frames: extractedFrames.map(f => f.dataUrl),
            previewFrames: extractedFrames.map(f => f.frameNumber),
            durationInFrames: overlayMeta.durationInFrames!,
            lastUpdated: Date.now(),
          })
        } else {
          console.warn('[useKeyframes] 未提取到关键帧，跳过缓存')
        }
      } catch (error) {
        console.error('[useKeyframes] 关键帧提取错误:', error)
        // 提取失败时清空帧数据
        setFrames([])
      } finally {
        setIsLoading(false)
        cleanup()
      }
    },
    [
      overlayMeta,
      cleanup,
      baseUrl,
    ]
  )

  React.useEffect(() => {
    if (ENABLE_VIDEO_KEYFRAME_EXTRACTING) {
      void performExtraction()
    }
    return cleanup
  }, [performExtraction])

  // Return empty arrays if disabled
  if (!ENABLE_VIDEO_KEYFRAME_EXTRACTING) {
    return {
      frames: [],
      previewFrames: [],
      isLoading: false,
    }
  }

  return {
    frames: frameData.dataUrls,
    previewFrames: frameData.frameNumbers,
    isLoading,
  }
}
