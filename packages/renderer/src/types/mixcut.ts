
export namespace Mixcut {
  export type SavedMixcut = {
    id: number
    scriptId: number
    name: string
    cover: string
    duration: number
    repetitionRate: number
    product: number
    url: string
    projectId: string
    createAt: any
    updateAt: any
    createTime: number
  }

  export type RequestRender = {
    scriptId: number;
    name: string;
    cover: string;
    duration: number;
    objectId: string;
    resolution: string;
    fps: number;

    bitRate: number;
    createAt: number;
    display: number;
    isSaveList: boolean;
    itemId: string;
    previewId: number;
    priority: number;
    product: number;
    repetitionRate: number;
    [property: string]: any;
  }
}
