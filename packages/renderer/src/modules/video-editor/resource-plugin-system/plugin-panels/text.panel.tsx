import React, { memo } from 'react'

import { OverlayType, TextOverlay } from '@clipnest/remotion-shared/types'
import { TEXT_DEFAULT_CLOUD_FONT_NAME, TEXT_DEFAULT_CLOUD_FONT_SRC } from '@/modules/video-editor/constants'

import StyledTextSelector from '@/modules/video-editor/components/common/styled-text-selector'
import { TextLayerRenderer } from '@clipnest/overlay-renderer'

const TextPanel: React.FC = () => {
  // 渲染默认文字预设项
  const renderDefaultTextItem = () => {
    const defaultPreviewOverlay: TextOverlay = {
      id: 0,
      type: OverlayType.TEXT,
      src: TEXT_DEFAULT_CLOUD_FONT_SRC,
      content: '默认',
      left: 0,
      top: 0,
      width: 80,
      height: 80,
      durationInFrames: 90,
      from: 0,
      rotation: 0,
      styles: {
        fontSize: 28,
        fontWeight: 'normal' as const,
        color: '#ffffff',
        fontFamily: TEXT_DEFAULT_CLOUD_FONT_NAME,
        fontStyle: 'normal' as const,
        underlineEnabled: false,
        textAlign: 'center' as const,
        backgroundColor: 'transparent',
        zIndex: 20,
        strokeEnabled: false,
        strokeWidth: 0,
        strokeColor: '#000000',
        shadowEnabled: false,
        shadowDistance: 0,
        shadowAngle: 45,
        shadowBlur: 2,
        shadowColor: '#000000',
        shadowOpacity: 0.5,
        backgroundImage: undefined,
        bubbleTextRect: undefined,
      }
    }

    return (
      <div
        key="default-text"
        className="group relative overflow-hidden border bg-gray-200 dark:bg-background rounded border-white/10 transition-all dark:hover:border-white/20 hover:border-blue-500/80 cursor-pointer aspect-square w-20"
      >
        <div className="h-full w-full flex items-center justify-center rounded">
          <div className="text-base transform-gpu transition-transform group-hover:scale-102 dark:text-white text-gray-900/90 size-full flex justify-center items-center">
            <TextLayerRenderer overlay={defaultPreviewOverlay} />
          </div>
        </div>

        {/* Label */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate">
          默认文字
        </div>
      </div>
    )
  }

  return (
    <div className="h-full">
      <div className="flex flex-wrap gap-3 p-2">
        {/* 默认文字预设 */}
        {renderDefaultTextItem()}
      </div>

      {/* 花体字选择器 */}
      <StyledTextSelector className="h-full" itemDraggable={true} />
    </div>
  )
}

export default memo(TextPanel)
