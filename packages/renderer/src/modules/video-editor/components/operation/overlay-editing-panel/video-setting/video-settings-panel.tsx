import React from 'react'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import { AnimationSettings } from '../../../shared/animation-preview'
import { animationTemplates } from '@clipnest/remotion-shared/constants'
import { useOverlayEditing } from '@/modules/video-editor/contexts'

export const VideoSettingsPanel: React.FC = () => {
  const { localOverlay: videoOverlay, updateEditingOverlay } = useOverlayEditing<VideoOverlay>()

  const handleEnterAnimationSelect = (animationKey: string) => {
    updateEditingOverlay({ styles: {
      animation: {
        ...videoOverlay?.styles?.animation,
        enter: animationKey,
      },
    } }, true)
  }

  const handleExitAnimationSelect = (animationKey: string) => {
    updateEditingOverlay({ styles: {
      animation: {
        ...videoOverlay?.styles?.animation,
        exit: animationKey,
      },
    } }, true)
  }

  return (
    <div className="space-y-2">
      <AnimationSettings
        animations={animationTemplates}
        selectedEnterAnimation={videoOverlay?.styles?.animation?.enter}
        selectedExitAnimation={videoOverlay?.styles?.animation?.exit}
        onEnterAnimationSelect={handleEnterAnimationSelect}
        onExitAnimationSelect={handleExitAnimationSelect}
      />
    </div>
  )
}
