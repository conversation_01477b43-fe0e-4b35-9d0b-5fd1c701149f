import { useEffect } from 'react'

export const useDisbandPageScale = () => {
  useEffect(() => {
    const preventZoomKeys = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) &&
        (e.key === '+' || e.key === '-' || e.key === '=' || e.key === '0')) {
        e.preventDefault()
      }
    }

    const preventZoomWheel = (e: WheelEvent) => {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault()
      }
    }

    const preventGesture = (e: Event) => {
      e.preventDefault()
    }

    document.addEventListener('keydown', preventZoomKeys)
    document.addEventListener('wheel', preventZoomWheel, { passive: false })

    document.addEventListener('gesturestart', preventGesture)
    document.addEventListener('gesturechange', preventGesture)
    document.addEventListener('gestureend', preventGesture)

    return () => {
      document.removeEventListener('keydown', preventZoomKeys)
      document.removeEventListener('wheel', preventZoomWheel)

      document.removeEventListener('gesturestart', preventGesture)
      document.removeEventListener('gesturechange', preventGesture)
      document.removeEventListener('gestureend', preventGesture)
    }
  }, [])
}
