import React from 'react'
import { Mixcut } from '@/types/mixcut'
import { useMixcutContext, useVirtualTab } from '@/contexts'
import { MultiSelectableCard } from './multi-selectable-card'
import { clsx } from 'clsx'
import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { EditorModule } from '@/libs/request/api/editor'

const SavedMixcutCard: React.FC<Mixcut.SavedMixcut & { index: number }> = ({ index: i, ...mixcut }) => {
  const { saved } = useMixcutContext()

  return (
    <MultiSelectableCard {...saved} index={i}>
      <div
        className={clsx(
          'w-48 h-64 relative outline-3 cursor-pointer ',
        )}
      >
        {/* 预览图片背景 */}
        <img
          src={mixcut.cover}
          alt={`saved-mixcut-${mixcut.id}`}
          className="w-full h-full object-cover rounded-sm"
          onError={e => {
            // 图片加载失败时显示默认背景
            e.currentTarget.style.display = 'none'
          }}
        />

        {/* 重复率标签 */}
        <div className="absolute right-[-8px] top-[-8px] bg-black/70 text-white p-1 text-xs rounded">
          重复率{(mixcut.repetitionRate * 100).toFixed(1)}%
        </div>
      </div>
    </MultiSelectableCard>
  )
}

export const SavedMixcutListPanel = () => {
  const { params } = useVirtualTab('Mixcut')
  const { id: scriptId } = params
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId],
    queryFn: () => EditorModule.listMixcuts(scriptId)
  })

  return (
    <div className="flex-1 h-fit flex flex-wrap gap-x-4 gap-y-6 p-4 overflow-y-auto">
      {data?.list.map((combo, i) => (
        <SavedMixcutCard
          {...combo}
          key={i}
          index={i}
        />
      ))}
    </div>
  )
}
