import React from 'react'
import TabBar from './TabBar'
import { useLocation } from 'react-router'

/**
 * TabManager 组件 - 多标签系统的核心容器
 * 负责渲染标签栏和标签内容
 */
const TabManager: React.FC = () => {
  const location = useLocation()

  if (!location.pathname.startsWith('/home')) return null

  return (
    <div className="flex flex-col h-full w-full">
      {/* 标签栏 */}
      <TabBar />
    </div>
  )
}

export default TabManager
