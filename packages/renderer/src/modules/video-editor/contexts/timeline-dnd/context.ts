import { createContext, RefObject, useContext } from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'
import { GhostElement } from '../../types'
import { DraggableState, OverlayDragInfo, OverlaysAdjustment } from './types'

export interface State {
  /**
   * 指示当前是否在进行拖拽
   */
  isDragging: boolean

  /**
   * 当前拖拽中的 Overlay
   */
  draggingOverlay: Overlay | null

  /**
   * 用于展示鼠标当前拖动位置
   */
  mousePosition: GhostElement | null

  /**
   * 用于指示当前拖拽的 Overlay 的最终落点
   */
  landingPoint: GhostElement | null

  /**
   * 用于预览即将会发生的 Overlay 飘移
   */
  previewOverlaysAdjust: OverlaysAdjustment

  /**
   * 当前拖拽信息的引用
   */
  dragInfoRef: RefObject<OverlayDragInfo | null>

  alignmentLines: number[]
}

interface Actions {
  setIsDragging: (isDragging: boolean) => void
  setMousePosition: (position: GhostElement | null) => void
  setLandingPoint: (point: GhostElement | null) => void
  setPreviewOverlaysAdjust: (adjust: OverlaysAdjustment) => void
  resetDragState: () => void

  /**
   * 处理 TimelineItem 拖拽结束/时长调整开始的公共处理方法
   */
  handleTimelineItemAdjustStart(overlay: Overlay): void

  /**
   * 处理 TimelineItem 拖拽结束/时长调整结束的公共处理方法
   */
  handleTimelineItemAdjustEnd(): void

  updateDraggableState(newVal: DraggableState): void
}

export type TimelineDndContextValues = State & Actions

export const TimelineDndContext = createContext<TimelineDndContextValues | undefined>(undefined)

export const useTimelineDnd = (): TimelineDndContextValues => {
  const context = useContext(TimelineDndContext)
  if (context === undefined) {
    throw new Error('useDragContext must be used within a DragProvider')
  }
  return context
}
