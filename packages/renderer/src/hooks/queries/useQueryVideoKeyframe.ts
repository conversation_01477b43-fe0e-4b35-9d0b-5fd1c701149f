import { useQuery } from '@tanstack/react-query'
import { KeyframeData } from '@/libs/cache/parts/keyframe.cache'
import { cacheManager } from '@/libs/cache/cache-manager'

export async function queryFirstKeyframeOfVideo(src?: string) {
  if (!src) return null

  try {
    // 从缓存管理器获取关键帧数据
    const keyframeData: KeyframeData | null = await cacheManager.keyframe.getKeyframes(src)

    if (!keyframeData) {
      console.warn(`视频 ${src} 的关键帧数据不存在`)
      return null
    }

    // 检查关键帧数据的完整性
    if (!keyframeData.frames || !keyframeData.frames.length) {
      console.warn(`视频 ${src} 的关键帧数据为空`)
      return null
    }

    // 返回第一帧作为预览图
    return keyframeData.frames[0] || null
  } catch (error) {
    console.error('获取混剪预览关键帧失败:', error)
    return null
  }
}

export const useQueryVideoKeyframe = (src?: string, enabled?: boolean) => {
  return useQuery({
    queryKey: ['COMBO_PREVIEW_KEYFRAME', src],
    queryFn: async (): Promise<string | null> => {
      return queryFirstKeyframeOfVideo(src)
    },
    enabled
  })
}
