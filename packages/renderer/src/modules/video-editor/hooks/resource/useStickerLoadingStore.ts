import { create } from 'zustand'

export type LoadingLayer = 'cover' | 'thumb' | 'file'

export interface StickerLoadingState {
  stickerId: string | number
  coverLoaded: boolean
  thumbLoaded: boolean
  thumbLoading: boolean
  fileLoaded: boolean
  fileLoading: boolean
  currentLayer: LoadingLayer
  lastUpdated: number
}

export interface StickerLoadingStore {
  // 状态存储 - 使用 Map 来存储每个贴纸的状态
  loadingStates: Map<string | number, StickerLoadingState>

  // 状态查询 - 统一 API 风格
  isStickerLoading: (stickerId: string | number, layer?: LoadingLayer) => boolean
  getStickerState: (stickerId: string | number) => StickerLoadingState

  // 状态更新 - 统一 API 风格
  startStickerLoading: (stickerId: string | number, layer: LoadingLayer) => boolean
  endStickerLoading: (stickerId: string | number, layer: LoadingLayer, success?: boolean) => boolean

  // 批量操作
  clearAllStates: () => void
  clearStickerState: (stickerId: string | number) => void

  // 内部方法
  _generateKey: (stickerId: string | number) => string
}

/**
 * 创建默认的贴纸加载状态
 */
const createDefaultState = (stickerId: string | number): StickerLoadingState => ({
  stickerId,
  coverLoaded: false,
  thumbLoaded: false,
  thumbLoading: false,
  fileLoaded: false,
  fileLoading: false,
  currentLayer: 'cover',
  lastUpdated: Date.now()
})

/**
 * 生成贴纸的唯一键值
 */
const generateKey = (stickerId: string | number): string => {
  return stickerId.toString()
}

/**
 * 贴纸加载状态管理 Store
 * 使用 Zustand 进行状态管理，支持贴纸的三层加载逻辑
 * API 设计与 useResourceLoadingStore 保持一致
 */
export const useStickerLoadingStore = create<StickerLoadingStore>((set, get) => ({
  loadingStates: new Map<string | number, StickerLoadingState>(),
  _generateKey: generateKey,

  /**
   * 检查指定贴纸的指定层级是否正在加载
   * @param stickerId 贴纸ID
   * @param layer 可选的层级，如果不指定则检查任意层级是否在加载
   * @returns 是否正在加载
   */
  isStickerLoading: (stickerId: string | number, layer?: LoadingLayer): boolean => {
    if (!stickerId) return false
    const { loadingStates } = get()

    const state = loadingStates.get(stickerId)
    if (!state) return false

    if (layer) {
      // 检查指定层级
      switch (layer) {
        case 'thumb':
          return state.thumbLoading
        case 'file':
          return state.fileLoading
        case 'cover':
          return false // cover 层不需要加载状态
        default:
          return false
      }
    } else {
      // 检查任意层级是否在加载
      return state.thumbLoading || state.fileLoading
    }
  },

  /**
   * 获取指定贴纸的完整加载状态
   * 如果不存在则创建默认状态
   */
  getStickerState: (stickerId: string | number): StickerLoadingState => {
    const { loadingStates } = get()

    if (!loadingStates.has(stickerId)) {
      const defaultState = createDefaultState(stickerId)

      // 创建新状态并更新 store
      set(state => {
        const newLoadingStates = new Map(state.loadingStates)
        newLoadingStates.set(stickerId, defaultState)
        return { loadingStates: newLoadingStates }
      })

      return defaultState
    }

    return loadingStates.get(stickerId)!
  },

  /**
   * 开始指定贴纸的指定层级加载
   * @param stickerId 贴纸ID
   * @param layer 加载层级
   * @returns 操作是否成功
   */
  startStickerLoading: (stickerId: string | number, layer: LoadingLayer): boolean => {
    try {
      // 参数验证
      if (!stickerId || !layer) {
        console.warn('[StickerLoadingStore] startStickerLoading: 参数无效', { stickerId, layer })
        return false
      }

      if (!['cover', 'thumb', 'file'].includes(layer)) {
        console.error('[StickerLoadingStore] startStickerLoading: 无效的层级', layer)
        return false
      }

      set(state => {
        const newLoadingStates = new Map(state.loadingStates)
        const currentState = newLoadingStates.get(stickerId) || createDefaultState(stickerId)

        const updatedState: StickerLoadingState = {
          ...currentState,
          currentLayer: layer,
          lastUpdated: Date.now()
        }

        // 根据层级设置对应的加载状态
        switch (layer) {
          case 'thumb':
            updatedState.thumbLoading = true
            break
          case 'file':
            updatedState.fileLoading = true
            break
          case 'cover':
            // cover 层不需要加载状态
            break
        }

        newLoadingStates.set(stickerId, updatedState)

        console.log('[StickerLoadingStore] 开始加载:', {
          stickerId,
          layer,
          newState: updatedState
        })

        return { loadingStates: newLoadingStates }
      })

      return true
    } catch (error) {
      console.error('[StickerLoadingStore] startStickerLoading 失败:', error)
      return false
    }
  },

  /**
   * 结束指定贴纸的指定层级加载
   * @param stickerId 贴纸ID
   * @param layer 加载层级
   * @param success 是否加载成功，默认为 true
   * @returns 操作是否成功
   */
  endStickerLoading: (stickerId: string | number, layer: LoadingLayer, success: boolean = true): boolean => {
    try {
      // 参数验证
      if (!stickerId || !layer) {
        console.warn('[StickerLoadingStore] endStickerLoading: 参数无效', { stickerId, layer })
        return false
      }

      if (!['cover', 'thumb', 'file'].includes(layer)) {
        console.error('[StickerLoadingStore] endStickerLoading: 无效的层级', layer)
        return false
      }

      set(state => {
        const newLoadingStates = new Map(state.loadingStates)
        const currentState = newLoadingStates.get(stickerId) || createDefaultState(stickerId)

        const updatedState: StickerLoadingState = {
          ...currentState,
          lastUpdated: Date.now()
        }

        // 根据层级设置对应的加载状态
        switch (layer) {
          case 'thumb':
            updatedState.thumbLoading = false
            if (success) {
              updatedState.thumbLoaded = true
            }
            break
          case 'file':
            updatedState.fileLoading = false
            if (success) {
              updatedState.fileLoaded = true
            }
            break
          case 'cover':
            if (success) {
              updatedState.coverLoaded = true
            }
            break
        }

        newLoadingStates.set(stickerId, updatedState)

        console.log('[StickerLoadingStore] 结束加载:', {
          stickerId,
          layer,
          success,
          newState: updatedState
        })

        return { loadingStates: newLoadingStates }
      })

      return true
    } catch (error) {
      console.error('[StickerLoadingStore] endStickerLoading 失败:', error)
      return false
    }
  },

  /**
   * 清除指定贴纸的状态
   */
  clearStickerState: (stickerId: string | number) => {
    set(state => {
      const newLoadingStates = new Map(state.loadingStates)
      newLoadingStates.delete(stickerId)
      
      console.log('[StickerLoadingStore] 清除贴纸状态:', { stickerId })
      
      return { loadingStates: newLoadingStates }
    })
  },

  /**
   * 清除所有贴纸状态
   */
  clearAllStates: () => {
    set(() => {
      console.log('[StickerLoadingStore] 清除所有贴纸状态')
      return { loadingStates: new Map() }
    })
  }
}))

/**
 * 选择器：获取指定贴纸的状态
 */
export const selectStickerState = (stickerId: string | number) => 
  (state: StickerLoadingStore) => state.getStickerState(stickerId)

/**
 * 选择器：检查指定贴纸是否正在加载任何层级
 */
export const selectStickerIsLoading = (stickerId: string | number, layer?: LoadingLayer) =>
  (state: StickerLoadingStore) => state.isStickerLoading(stickerId, layer)

/**
 * 选择器：获取指定贴纸的当前层级
 */
export const selectStickerCurrentLayer = (stickerId: string | number) => 
  (state: StickerLoadingStore) => state.getStickerState(stickerId).currentLayer
