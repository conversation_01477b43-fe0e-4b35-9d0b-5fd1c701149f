import { useCallback } from 'react'
import { useEditorContext } from '../../contexts/editor/context'
import { OverlayDragInfo, useTimelineContext, useTimelineDnd } from '@/modules/video-editor/contexts'
import { calculateDraggableStateForMoving, snapToGrid } from '../../contexts/timeline-dnd/utils'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { clamp } from 'lodash'

/**
 * 处理 TimelineItem 在时间轴上拖动
 */
export const useTimelineItemMoving = () => {
  const { zoomScale } = useTimelineContext()
  const { tracks, durationInFrames } = useEditorContext()
  const { dragInfoRef, updateDraggableState } = useTimelineDnd()

  const calculatePositionInfo = useCallback((
    dragInfo: OverlayDragInfo,
    deltaX: number,
    targetTrackIndex?: number
  ) => {
    const deltaFrame = snapToGrid(deltaX / zoomScale / PIXELS_PER_FRAME)

    // 使用传入的 targetTrackIndex，如果没有则使用初始轨道
    const targetRow = targetTrackIndex !== undefined
      ? clamp(targetTrackIndex, 0, tracks.length - 1)
      : dragInfo.initialRow

    const targetStartFrame: number = Math.max(0, dragInfo.initialFrom + deltaFrame)

    return {
      targetRow,
      targetStartFrame,
    }
  }, [durationInFrames, zoomScale, tracks.length])

  const handleOverlayDragMove = useCallback(
    (deltaX: number, targetTrackIndex: number) => {
      if (!dragInfoRef.current) return

      const { targetRow, targetStartFrame } = calculatePositionInfo(
        dragInfoRef.current,
        deltaX,
        targetTrackIndex
      )

      dragInfoRef.current.currentRow = targetRow
      dragInfoRef.current.currentFrom = targetStartFrame

      updateDraggableState(
        calculateDraggableStateForMoving(
          tracks,
          dragInfoRef.current.overlay,
          targetStartFrame,
          { ...tracks[targetRow], index: targetRow }
        )
      )
    },
    [calculatePositionInfo, tracks],
  )

  return {
    handleOverlayDragMove,
  }
}
