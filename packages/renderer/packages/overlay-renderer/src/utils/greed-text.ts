import * as opentype from 'opentype.js'

export class TextWidthCacheManager {

  private static instance: TextWidthCacheManager | null = null

  private cache = new Map<string, number>()

  static getInstance(): TextWidthCacheManager {
    if (!TextWidthCacheManager.instance) {
      TextWidthCacheManager.instance = new TextWidthCacheManager()
    }
    return TextWidthCacheManager.instance
  }

  private getCacheKey(fontPath: string, content: string, fontSize: number, letterSpacing?: number): string {
    return letterSpacing !== undefined
      ? `${fontPath}:${content}:${fontSize}:${letterSpacing}`
      : `${fontPath}:${content}:${fontSize}`
  }

  getCachedWidth(fontPath: string, content: string, fontSize: number, letterSpacing?: number): number | null {
    const key = this.getCacheKey(fontPath, content, fontSize, letterSpacing)
    return this.cache.get(key) || null
  }

  setCachedWidth(fontPath: string, content: string, fontSize: number, width: number, letterSpacing?: number): void {
    const key = this.getCacheKey(fontPath, content, fontSize, letterSpacing)
    this.cache.set(key, width)
  }
}

/**
 * 贪心算法文本换行工具
 * 基于固定的字体大小和容器宽度，动态计算每行能容纳多少个字符
 * 支持字间距计算
 */
export class GreedyTextWrapper {

  private readonly fontPath: opentype.Font

  private readonly fontSize: number

  private readonly containerWidth: number

  private readonly letterSpacing: number

  constructor(fontPath: opentype.Font, fontSize: number, containerWidth: number, letterSpacing: number = 0) {
    this.fontPath = fontPath
    this.fontSize = fontSize
    this.containerWidth = containerWidth
    this.letterSpacing = letterSpacing
  }

  /**
   * 获取文本片段的宽度（包含字间距）
   */
  private getTextWidth(text: string): number {
    if (!text) return 0

    try {
      const baseWidth = this.fontPath.getAdvanceWidth(text, this.fontSize)
      // 字间距：字符数量减1乘以字间距（最后一个字符后不加字间距）
      const letterSpacingWidth = Math.max(0, text.length - 1) * this.letterSpacing
      return baseWidth + letterSpacingWidth
    } catch (error) {
      console.warn('[贪心换行] 获取文本宽度失败:', text, error)
      // 使用字符数量估算
      const baseWidth = text.length * this.fontSize * 0.6
      const letterSpacingWidth = Math.max(0, text.length - 1) * this.letterSpacing
      return baseWidth + letterSpacingWidth
    }
  }

  /**
   * 贪心算法：考虑单词边界
   * @param content 要分割的文本内容
   * @param respectWordBoundary 是否尊重单词边界（对中文无效）
   * @returns 分割后的字符串数组
   */
  public wrapText(content: string, respectWordBoundary: boolean = false): string[] {
    if (!content || !this.fontPath) {
      return [content || '']
    }

    const lines: string[] = []
    const paragraphs = content.split('\n')

    for (const paragraph of paragraphs) {
      if (!paragraph.trim()) {
        lines.push('')
        continue
      }

      if (respectWordBoundary && /^[a-zA-Z\s]+$/.test(paragraph)) {
        // 英文文本，按单词分割
        this.wrapEnglishText(paragraph, lines)
      } else {
        // 中文或混合文本，按字符分割
        this.wrapChineseText(paragraph, lines)
      }
    }

    return lines.length > 0 ? lines : ['']
  }

  /**
   * 处理英文文本换行（按单词）
   */
  private wrapEnglishText(paragraph: string, lines: string[]): void {
    const words = paragraph.split(/(\s+)/) // 保留空格
    let currentLine = ''
    let currentWidth = 0

    for (const word of words) {
      const wordWidth = this.getTextWidth(word)

      if (currentWidth + wordWidth > this.containerWidth && currentLine.trim().length > 0) {
        lines.push(currentLine.trimEnd())
        currentLine = word
        currentWidth = wordWidth
      } else {
        currentLine += word
        currentWidth += wordWidth
      }
    }

    if (currentLine.trim().length > 0) {
      lines.push(currentLine.trimEnd())
    }
  }

  /**
   * 处理中文文本换行（按字符）
   */
  private wrapChineseText(paragraph: string, lines: string[]): void {
    let currentLine = ''
    let currentWidth = 0

    for (let i = 0; i < paragraph.length; i++) {
      const char = paragraph[i]
      const charBaseWidth: number = this.fontPath.getAdvanceWidth(char, this.fontSize)

      // 计算添加这个字符后的总宽度
      // 如果是第一个字符，不加字间距；否则加上字间距
      const charTotalWidth = currentLine.length === 0
        ? charBaseWidth
        : charBaseWidth + this.letterSpacing

      if (currentWidth + charTotalWidth > this.containerWidth && currentLine.length > 0) {
        lines.push(currentLine)
        currentLine = char
        currentWidth = charBaseWidth
      } else {
        currentLine += char
        currentWidth += charTotalWidth
      }
    }

    if (currentLine.length > 0) {
      lines.push(currentLine)
    }
  }

  /**
   * 获取换行后的总高度
   * @param lines 文本行数组
   * @param lineHeight 自定义行高，如果不提供则使用默认值
   */
  // public getWrappedTextHeight(lines: string[], lineHeight?: number): number {
  //   const actualLineHeight = lineHeight || this.fontSize * 1.2
  //   return lines.length * actualLineHeight
  // }

  /**
   * 获取每行的详细信息
   * @param lines 文本行数组
   * @param lineHeight 自定义行高，如果不提供则使用默认值
   */
  // public getLineDetails(lines: string[], lineHeight?: number): Array<{ line: string, width: number, height: number }> {
  //   const actualLineHeight = lineHeight || this.fontSize * 1.2
  //
  //   return lines.map(line => ({
  //     line,
  //     width: this.getTextWidth(line),
  //     height: actualLineHeight
  //   }))
  // }

  /**
   * 获取换行后的总高度（包含行间距）
   * @param lines 文本行数组
   * @param lineHeight 行高，如果不提供则使用默认值
   * @param lineSpacingRatio 行间距倍数，默认为0
   */
  public getWrappedTextHeightWithSpacing(lines: string[], lineHeight?: number, lineSpacingRatio: number = 0): number {
    const actualLineHeight = lineHeight || this.fontSize * 1.2
    const lineCount = lines.length

    // 计算实际行间距像素值：行间距倍数 * 字体大小
    const actualLineSpacing = lineSpacingRatio * this.fontSize

    // 总高度 = 行数 * 行高 + (行数 - 1) * 实际行间距
    return (lineCount * actualLineHeight) + (Math.max(0, lineCount - 1) * actualLineSpacing)
  }

  /**
   * 获取每行的详细信息（包含行间距）
   * @param lines 文本行数组
   * @param lineHeight 行高，如果不提供则使用默认值
   * @param lineSpacingRatio 行间距倍数，默认为0
   */
  public getLineDetailsWithSpacing(
    lines: string[],
    lineHeight?: number,
    lineSpacingRatio: number = 0
  ): Array<{ line: string, width: number, height: number, y: number }> {
    const actualLineHeight = lineHeight || this.fontSize * 1.2

    // 计算实际行间距像素值：行间距倍数 * 字体大小
    const actualLineSpacing = lineSpacingRatio * this.fontSize

    return lines.map((line, index) => ({
      line,
      width: this.getTextWidth(line),
      height: actualLineHeight,
      y: index * (actualLineHeight + actualLineSpacing) // 每行的Y坐标包含行间距
    }))
  }
}
