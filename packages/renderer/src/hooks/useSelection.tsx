import { useState, useMemo, useCallback, useEffect } from 'react'

interface UseSelectionProps<Media, Folder> {
  mediaList?: { pages: { list: Media[] }[] }
  folderAsMediaItems?: Folder[] // 变为可选
  getMediaId: (item: Media) => string
  getFolderId?: (item: Folder) => string // 可选
}

export function useSelection<Media, Folder>({
  mediaList,
  folderAsMediaItems = [], // 默认为空数组
  getMediaId,
  getFolderId,
}: UseSelectionProps<Media, Folder>) {
  const hasFolder = !!getFolderId // 判断是否需要 folder 逻辑

  const [selectedMediaItems, setSelectedMediaItems] = useState<Set<string>>(new Set())
  const [selectedFolderItems, setSelectedFolderItems] = useState<Set<string>>(new Set())
  const [isSelectAllMode, setIsSelectAllMode] = useState(false)

  /** 所有 ID 列表 */
  const allMediaIds = useMemo(
    () => (mediaList?.pages ?? []).flatMap(page => page.list.map(getMediaId)),
    [mediaList, getMediaId],
  )
  const allFolderIds = useMemo(
    () => (hasFolder ? folderAsMediaItems.map(getFolderId!) : []),
    [folderAsMediaItems, getFolderId, hasFolder],
  )

  /** 是否全选 */
  const allSelected = useMemo(() => {
    const totalCount = allMediaIds.length + (hasFolder ? allFolderIds.length : 0)
    if (totalCount === 0) return false
    const mediaSelected = allMediaIds.every(id => selectedMediaItems.has(id))
    const folderSelected = hasFolder ? allFolderIds.every(id => selectedFolderItems.has(id)) : true
    return mediaSelected && folderSelected
  }, [allMediaIds, allFolderIds, selectedMediaItems, selectedFolderItems, hasFolder])

  /** 单选/取消单选 */
  const toggleSelect = useCallback(
    (fileId: string, isFolder?: boolean) => {
      if (isFolder && hasFolder) {
        setSelectedFolderItems(prev => {
          const next = new Set(prev)
          if (next.has(fileId)) {
            next.delete(fileId)
          } else {
            next.add(fileId)
          }
          return next
        })
      } else {
        setSelectedMediaItems(prev => {
          const next = new Set(prev)
          if (next.has(fileId)) {
            next.delete(fileId)
          } else {
            next.add(fileId)
          }
          return next
        })
      }
      setIsSelectAllMode(false)
    },
    [hasFolder],
  )

  /** 全选/取消全选 */
  const toggleSelectAll = useCallback(() => {
    if (isSelectAllMode || allSelected) {
      setSelectedMediaItems(new Set())
      setSelectedFolderItems(new Set())
      setIsSelectAllMode(false)
    } else {
      setSelectedMediaItems(new Set(allMediaIds))
      if (hasFolder) setSelectedFolderItems(new Set(allFolderIds))
      setIsSelectAllMode(true)
    }
  }, [isSelectAllMode, allSelected, allMediaIds, allFolderIds, hasFolder])

  /** 分页加载后自动补齐选中状态 */
  useEffect(() => {
    if (!isSelectAllMode) return

    const mediaIdsSet = new Set(allMediaIds)
    const folderIdsSet = new Set(allFolderIds)

    // 只有不一致时才更新
    const mediaChanged =
      selectedMediaItems.size !== mediaIdsSet.size || [...selectedMediaItems].some(id => !mediaIdsSet.has(id))
    const folderChanged =
      hasFolder &&
      (selectedFolderItems.size !== folderIdsSet.size || [...selectedFolderItems].some(id => !folderIdsSet.has(id)))

    if (mediaChanged) setSelectedMediaItems(mediaIdsSet)
    if (folderChanged) setSelectedFolderItems(folderIdsSet)
  }, [isSelectAllMode, allMediaIds, allFolderIds, hasFolder, selectedMediaItems, selectedFolderItems])

  /** 当前文件和文件夹选中个数 */
  const selectedCount = selectedMediaItems.size + (hasFolder ? selectedFolderItems.size : 0)

  /** 当前文件和文件夹总数 */
  const materialCount = useMemo(() => {
    const mediaItemsCount = mediaList?.pages?.reduce((total, page) => total + (page.list?.length || 0), 0) ?? 0
    return mediaItemsCount + (hasFolder ? folderAsMediaItems.length : 0)
  }, [mediaList, folderAsMediaItems, hasFolder])

  /** 选中数量的文字 */
  const selectedText = useMemo(() => {
    const parts: string[] = []
    if (selectedMediaItems.size > 0) {
      parts.push(`${selectedMediaItems.size}个文件`)
    }
    if (hasFolder && selectedFolderItems.size > 0) {
      parts.push(`${selectedFolderItems.size}个文件夹`)
    }
    return parts.join('，')
  }, [selectedMediaItems, selectedFolderItems, hasFolder])

  /** 清空选中 */
  const clearSelection = useCallback(() => {
    setSelectedMediaItems(new Set())
    if (hasFolder) setSelectedFolderItems(new Set())
    setIsSelectAllMode(false)
  }, [hasFolder])

  return {
    selectedMediaItems,
    selectedFolderItems,
    setSelectedMediaItems,
    setSelectedFolderItems,
    toggleSelect,
    toggleSelectAll,
    allSelected,
    selectedCount,
    materialCount,
    selectedText,
    clearSelection,
  }
}
