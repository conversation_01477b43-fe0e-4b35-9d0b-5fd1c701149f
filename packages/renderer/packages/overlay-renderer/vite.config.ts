import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    react()
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/entry'),
      name: 'Overlay<PERSON>enderer',
      formats: ['es'],
      fileName: 'entry'
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'remotion',
        'opentype.js'
      ],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          remotion: 'Remotion'
        }
      }
    },
    sourcemap: false,
    emptyOutDir: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      '@clipnest/remotion-shared': resolve(__dirname, '../remotion-shared')
    }
  }
})
