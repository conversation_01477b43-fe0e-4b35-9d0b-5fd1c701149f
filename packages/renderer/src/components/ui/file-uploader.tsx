import React, { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { AlertCircle, CheckCircle, FileVideo, Image, Loader2, Upload, X } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { Button } from './button'
import { uploadBufferViaIPC } from '@/libs/request/upload'
import { md5 } from '@/libs/tools/md5'
import { UploadModule } from '@app/shared/types/ipc/file-uploader'

export interface UploadedFile {
  id: string
  file: File
  preview?: string
  status: 'uploading' | 'success' | 'error'
  error?: string
  url?: string
  fileName?: string,
  progress?: number // 上传进度 (0-1)
  objectId?: string
  fileMd5?: string
  folderUuid: string
}

export interface FileUploaderRenderProps {
  isDragActive: boolean
  getRootProps: () => any
  getInputProps: () => any
  uploadedFiles: UploadedFile[]
  removeFile: (fileId: string) => void
  disabled: boolean
  isLoading: boolean
  fileTypes: string[]
  maxSize: number
}

export interface FileUploaderProps {
  folderUuid: string,
  fileTypes?: string[]
  maxFiles?: number
  maxSize?: number
  multiple?: boolean
  onUpload?: (files: UploadedFile[]) => void
  onError?: (error: string) => void
  className?: string
  disabled?: boolean
  value?: UploadedFile[]
  onChange?: (files: UploadedFile[]) => void
  renderCustomComponent?: (props: FileUploaderRenderProps) => React.ReactNode
  module?: UploadModule
}

const FileUploader: React.FC<FileUploaderProps> = ({
  fileTypes = ['video/*', 'image/*', 'audio/mpeg'],
  maxFiles = 5,
  maxSize = 100 * 1024 * 1024, // 100MB
  multiple = true,
  onUpload,
  onError,
  className,
  disabled = false,
  value = [],
  onChange,
  folderUuid,
  renderCustomComponent,
  module = UploadModule.media
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>(value)
  const [lastUploadedIds, setLastUploadedIds] = useState<string[]>([])

  // 计算 loading 状态
  const isLoading = useMemo(() => {
    return uploadedFiles.some(file => file.status === 'uploading')
  }, [uploadedFiles])

  const updateFiles = useCallback((files: UploadedFile[]) => {
    setUploadedFiles(files)
    onChange?.(files)
  }, [onChange])

  const validateFile = useCallback((file: File): string | null => {
    if (file.size > maxSize) {
      return `文件大小不能超过 ${(maxSize / 1024 / 1024).toFixed(1)}MB`
    }

    const isValidType = fileTypes.some(type => {
      if (type.endsWith('/*')) {
        const category = type.split('/')[0]
        return file.type.startsWith(category + '/')
      }
      return file.type === type
    })

    if (!isValidType) {
      return `不支持的文件类型，支持: ${fileTypes.join(', ')}`
    }

    return null
  }, [fileTypes, maxSize])

  useEffect(() => {
    const successfulFiles = uploadedFiles.filter(f => f.status === 'success')
    const newSuccessfulFiles = successfulFiles.filter(f => !lastUploadedIds.includes(f.id))

    if (newSuccessfulFiles.length > 0) {
      onUpload?.(newSuccessfulFiles)
      setLastUploadedIds(successfulFiles.map(f => f.id)) // 更新记录
    }
  }, [uploadedFiles, lastUploadedIds, onUpload])

  const uploadFileToOSS = useCallback(async (uploadedFile: UploadedFile) => {
    try {
      const arrayBuffer = await uploadedFile.file.arrayBuffer()
      const fileMd5 = md5(arrayBuffer)
      const result = await uploadBufferViaIPC(
        arrayBuffer,
        uploadedFile.file.name,
        folderUuid,
        fileMd5,
        module,
        {
          dirPrefix: 'user-uploads/',
          onProgress: (progress: number) => {
          // 更新文件的上传进度
            setUploadedFiles(prev => {
              const updated = prev.map(f =>
                f.id === uploadedFile.id
                  ? {
                    ...f,
                    progress: progress
                  }
                  : f
              )
              onChange?.(updated)
              return updated
            })
          }
        })

      setUploadedFiles(prev => {
        const updated = prev.map(f =>
          f.id === uploadedFile.id
            ? {
              ...f,
              status: 'success' as const,
              url: result.url,
              fileName: result.fileName,
              objectId: result.objectId,
              folderUuid: folderUuid,
              fileMd5: fileMd5,
              progress: 1 // 完成时设置为100%
            }
            : f
        )
        onChange?.(updated)
        return updated
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败'
      setUploadedFiles(prev => {
        const updated = prev.map(f =>
          f.id === uploadedFile.id
            ? {
              ...f,
              status: 'error' as const,
              error: errorMessage
            }
            : f
        )
        onChange?.(updated)
        return updated
      })
      onError?.(errorMessage)
    }
  }, [onChange, onUpload, onError])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (disabled) return

    const totalFiles = uploadedFiles.length + acceptedFiles.length
    if (totalFiles > maxFiles) {
      onError?.(`最多只能上传 ${maxFiles} 个文件`)
      return
    }

    const newFiles: UploadedFile[] = acceptedFiles.map(file => {
      const validation = validateFile(file)
      if (validation) {
        onError?.(validation)
        return null
      }

      const id = Math.random().toString(36).substring(2, 11)
      const preview = file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined

      return {
        id,
        file,
        preview,
        folderUuid,
        status: 'uploading' as const
      }
    }).filter(Boolean) as UploadedFile[]

    const updatedFiles = [...uploadedFiles, ...newFiles]
    updateFiles(updatedFiles)

    newFiles.forEach(file => {
      uploadFileToOSS(file)
    })
  }, [uploadedFiles, maxFiles, validateFile, onError, disabled, updateFiles, uploadFileToOSS])

  const removeFile = useCallback((fileId: string) => {
    const updatedFiles = uploadedFiles.filter(f => f.id !== fileId)
    updateFiles(updatedFiles)
  }, [uploadedFiles, updateFiles])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: fileTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    multiple,
    disabled
  })

  const getFileIcon = useCallback((file: File) => {
    if (file.type.startsWith('video/')) {
      return <FileVideo className="w-8 h-8 text-blue-500" />
    }
    if (file.type.startsWith('image/')) {
      return <Image className="w-8 h-8 text-green-500" />
    }
    return <Upload className="w-8 h-8 text-gray-500" />
  }, [])

  const renderProps: FileUploaderRenderProps = useMemo(() => ({
    isDragActive,
    getRootProps,
    getInputProps,
    uploadedFiles,
    removeFile,
    disabled,
    isLoading,
    fileTypes,
    maxSize,
  }), [isDragActive, getRootProps, getInputProps, uploadedFiles, removeFile, disabled, isLoading, fileTypes, maxSize])

  if (renderCustomComponent) {
    return (
      <div className={cn('space-y-4', className)}>
        {renderCustomComponent(renderProps)}
      </div>
    )
  }

  // 默认渲染组件
  return (
    <div className={cn('space-y-4', className)}>
      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
          isDragActive
            ? 'border-primary bg-primary/5'
            : 'border-gray-300 dark:border-gray-600 hover:border-primary',
          disabled && 'opacity-50 cursor-not-allowed',
          isLoading && 'pointer-events-none opacity-75'
        )}
      >
        <input {...getInputProps()} />
        {isLoading ? (
          <Loader2 className="w-12 h-12 mx-auto mb-4 text-primary animate-spin" />
        ) : (
          <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        )}
        <p className="text-lg font-medium mb-2">
          {isLoading
            ? '正在上传...'
            : isDragActive
              ? '释放文件到这里'
              : '拖拽文件到这里或点击上传'}
        </p>
        <p className="text-sm text-gray-500">
          支持 {fileTypes.join(', ')} 格式，最大 {(maxSize / 1024 / 1024).toFixed(1)}MB
        </p>
      </div>

      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          {uploadedFiles.map(uploadedFile => (
            <div
              key={uploadedFile.id}
              className="flex items-center space-x-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800"
            >
              {/* File Icon/Preview */}
              <div className="flex-shrink-0">
                {uploadedFile.preview ? (
                  <img
                    src={uploadedFile.preview}
                    alt="Preview"
                    className="w-12 h-12 object-cover rounded"
                  />
                ) : (
                  getFileIcon(uploadedFile.file)
                )}
              </div>

              {/* File Info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">
                  {uploadedFile.file.name}
                </p>
                <p className="text-xs text-gray-500">
                  {(uploadedFile.file.size / 1024 / 1024).toFixed(2)} MB
                </p>

                {/* Status Display */}
                {uploadedFile.status === 'uploading' && (
                  <div className="mt-2">
                    <div className="flex items-center text-blue-500 mb-1">
                      <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                      <span className="text-xs">
                        正在上传... {uploadedFile.progress ? `${Math.round(uploadedFile.progress * 100)}%` : ''}
                      </span>
                    </div>
                    {/* 进度条 */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(uploadedFile.progress || 0) * 100}%` }}
                      />
                    </div>
                  </div>
                )}

                {uploadedFile.status === 'success' && (
                  <div className="flex items-center mt-2 text-green-500">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    <span className="text-xs">上传成功</span>
                    {uploadedFile.url && (
                      <div
                        className="text-xs text-blue-500 hover:text-blue-700 ml-2 underline"
                      >
                        {uploadedFile.url}
                      </div>
                    )}
                  </div>
                )}

                {uploadedFile.status === 'error' && (
                  <div className="flex items-center mt-2 text-red-500">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    <span className="text-xs">{uploadedFile.error}</span>
                  </div>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFile(uploadedFile.id)}
                className="text-gray-500 hover:text-red-500"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export { FileUploader }
