#!/usr/bin/env node

/**
 * Electron 应用自动更新文件上传脚本
 * 支持多平台（Windows/macOS/Linux）的构建产物上传到腾讯云 COS
 * 确保与 electron-builder 和 electron-updater 完全兼容
 */

import fs from 'fs'
import path from 'path'
import process from 'process'
import console from 'console'

/**
 * 平台配置映射
 * 定义了每个平台的文件特征和命名规则
 */
const PLATFORM_CONFIG = {
  win32: {
    keywords: ['win', 'windows'],
    extensions: ['.exe', '.msi'],
    ymlSuffix: '',
    archPatterns: ['x64', 'ia32', 'arm64']
  },
  darwin: {
    keywords: ['mac', 'darwin', 'osx'],
    extensions: ['.dmg', '.pkg'],
    ymlSuffix: '-mac',
    archPatterns: ['x64', 'arm64', 'universal']
  },
  linux: {
    keywords: ['linux'],
    extensions: ['.deb', '.rpm', '.tar.gz', '.AppImage'],
    ymlSuffix: '-linux',
    archPatterns: ['x64', 'ia32', 'arm64', 'armv7l']
  }
}

/**
 * 全局配置
 */
const CONFIG = {
  version: process.env.npm_package_version,
  channel: process.argv[2] || process.env.VITE_DISTRIBUTION_CHANNEL || 'latest',
  requiredEnvVars: ['COS_SECRET_ID', 'COS_SECRET_KEY', 'COS_REGION', 'COS_BUCKET'],
  cosBaseUrl: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com',
  platforms: Object.keys(PLATFORM_CONFIG)
}

/**
 * 工具函数集合
 */
class UploadUtils {

  /**
   * 获取构建目录
   */
  static getBuildDirectory() {
    const testDir = path.join(process.cwd(), 'dist-test')
    const prodDir = path.join(process.cwd(), 'dist')

    // 优先使用测试目录，如果存在且生产目录没有yml文件
    return fs.existsSync(testDir) && !fs.existsSync(path.join(prodDir, `${CONFIG.channel}.yml`))
      ? testDir
      : prodDir
  }

  /**
   * 检查环境变量
   */
  static checkEnvironmentVariables() {
    console.log('\n📋 检查环境变量...')
    for (const varName of CONFIG.requiredEnvVars) {
      if (!process.env[varName]) {
        throw new Error(`缺少环境变量: ${varName}`)
      }
    }
    console.log('✅ 环境变量检查通过')
  }

  /**
   * 检查构建目录
   */
  static checkBuildDirectory(distDir) {
    console.log('\n📁 检查构建目录...')
    if (!fs.existsSync(distDir)) {
      throw new Error(`构建目录不存在: ${distDir}`)
    }
    console.log('✅ 构建目录存在')
  }

  /**
   * 初始化 COS SDK
   */
  static async initializeCOS() {
    console.log('\n📦 导入 COS SDK...')
    const COS = await import('cos-nodejs-sdk-v5')
    const cos = new COS.default({
      SecretId: process.env.COS_SECRET_ID,
      SecretKey: process.env.COS_SECRET_KEY,
    })
    console.log('✅ COS SDK 初始化成功')
    return cos
  }

  /**
   * 判断是否为安装包文件
   */
  static isInstallerFile(fileName, ext) {
    // 检查是否为已知的安装包扩展名，且不在 unpacked 目录中
    const isKnownInstaller = Object.values(PLATFORM_CONFIG)
      .some(config => config.extensions.includes(ext))
    return isKnownInstaller && !fileName.includes('unpacked')
  }

  /**
   * 从文件名获取平台信息
   */
  static getPlatformFromFileName(fileName) {
    const lowerFileName = fileName.toLowerCase()

    for (const [platform, config] of Object.entries(PLATFORM_CONFIG)) {
      // 检查关键词匹配
      const hasKeyword = config.keywords.some(keyword => lowerFileName.includes(keyword))
      // 检查扩展名匹配
      const hasExtension = config.extensions.some(ext => lowerFileName.endsWith(ext))

      if (hasKeyword || hasExtension) {
        return platform
      }
    }
    return 'unknown'
  }

  /**
   * 从更新文件名获取平台信息
   */
  static getPlatformFromUpdateFile(fileName) {
    if (fileName.endsWith('-mac.yml')) return 'darwin'
    if (fileName.endsWith('-linux.yml')) return 'linux'
    if (fileName.endsWith('.yml')) return 'win32' // 默认情况为 Windows（无后缀）
    return 'unknown'
  }

  /**
   * 根据平台生成 YML 文件名
   */
  static generateYmlFileName(platform) {
    const config = PLATFORM_CONFIG[platform]
    const suffix = config?.ymlSuffix || ''
    return `${CONFIG.channel}${suffix}.yml`
  }

  /**
   * 生成 COS 存储路径
   */
  static generateCosKey(file) {
    if (file.type === 'yml') {
      const ymlFileName = this.generateYmlFileName(file.platform)
      return `clipnest-update/${CONFIG.channel}/latest/${ymlFileName}`
    } else {
      return `clipnest-update/${CONFIG.channel}/latest/${file.uploadName}`
    }
  }

  /**
   * 生成访问 URL
   */
  static generateAccessUrl(platform) {
    const ymlFileName = this.generateYmlFileName(platform)
    return `${CONFIG.cosBaseUrl}/clipnest-update/${CONFIG.channel}/latest/${ymlFileName}`
  }
}

/**
 * 扫描构建产物
 */
async function scanBuildArtifacts(distDir) {
  console.log('\n🔍 扫描构建产物...')
  const { glob } = await import('glob')

  // 更精确的文件过滤模式
  const allFiles = await glob(['**/*'], {
    cwd: distDir,
    nodir: true,
    ignore: [
      '*unpacked/**',
      '**/*unpacked/**',
      '**/unpacked/**',
      '**/*.log',
      '**/node_modules/**'
    ]
  })

  const updateFiles = []

  for (const file of allFiles) {
    const fileName = path.basename(file)
    const ext = path.extname(fileName)
    const filePath = path.join(distDir, file)

    // 跳过不需要的文件
    if (fileName.includes('unpacked') || fileName.includes('debug')) {
      continue
    }

    // 处理更新清单文件 (.yml)
    if (fileName.startsWith(CONFIG.channel) && fileName.endsWith('.yml')) {
      const platform = UploadUtils.getPlatformFromUpdateFile(fileName)

      updateFiles.push({
        localPath: filePath,
        originalName: fileName,
        uploadName: fileName,
        platform,
        type: 'yml'
      })
    }
    // 处理安装包文件
    else if (UploadUtils.isInstallerFile(fileName, ext)) {
      updateFiles.push({
        localPath: filePath,
        originalName: fileName,
        uploadName: fileName,
        platform: UploadUtils.getPlatformFromFileName(fileName),
        type: 'installer',
        size: fs.statSync(filePath).size,
      })
    }
    // 处理 blockmap 文件
    else if (fileName.endsWith('.blockmap')) {
      updateFiles.push({
        localPath: filePath,
        originalName: fileName,
        uploadName: fileName,
        platform: UploadUtils.getPlatformFromFileName(fileName),
        type: 'blockmap'
      })
    }
  }

  console.log(`📦 发现更新文件: ${updateFiles.length} 个`)
  updateFiles.forEach(file => {
    const rename = file.originalName !== file.uploadName ? ` -> ${file.uploadName}` : ''
    console.log(`  - ${file.type}: ${file.originalName}${rename} (${file.platform})`)
  })

  if (updateFiles.length === 0) {
    console.log('⚠️  没有找到需要上传的文件')
    return []
  }

  return updateFiles
}

/**
 * 上传文件到 COS
 */
async function uploadFiles(cos, updateFiles) {
  console.log('\n📤 开始上传文件...')
  let uploadCount = 0

  for (const file of updateFiles) {
    const cosKey = UploadUtils.generateCosKey(file)

    console.log(`📤 上传 ${file.type}: ${file.originalName} -> ${cosKey}`)

    const uploadOptions = {
      Bucket: process.env.COS_BUCKET,
      Region: process.env.COS_REGION,
      Key: cosKey,
      Body: fs.createReadStream(file.localPath),
      ContentLength: file.size || fs.statSync(file.localPath).size,
    }

    if (file.type === 'yml') {
      uploadOptions.ContentType = 'application/x-yaml'
    }

    await cos.putObject(uploadOptions)

    uploadCount++
    console.log(`✅ ${file.originalName} 上传完成`)
  }

  return uploadCount
}

/**
 * 显示上传摘要
 */
function displayUploadSummary(uploadCount, updateFiles) {
  console.log(`\n🎉 上传完成！共上传 ${uploadCount} 个文件`)
  console.log(`🌐 更新服务器: ${CONFIG.cosBaseUrl}/clipnest-update/${CONFIG.channel}/latest/`)

  console.log('\n📋 上传摘要:')
  console.log(`  版本: ${CONFIG.version}`)
  console.log(`  渠道: ${CONFIG.channel}`)
  console.log(`  文件数: ${uploadCount} 个`)

  // 显示各平台的更新清单 URL
  const platforms = [...new Set(updateFiles.filter(f => f.type === 'yml').map(f => f.platform))]
  if (platforms.length > 0) {
    console.log('\n🌐 更新清单文件访问链接:')
    platforms.forEach(platform => {
      const url = UploadUtils.generateAccessUrl(platform)
      console.log(`  ${platform}: ${url}`)
    })
  }
}

/**
 * 主要的自动上传方法
 * @param {Object} options - 上传选项
 * @param {string} options.channel - 发布渠道
 * @param {string} options.distDir - 构建目录
 * @param {boolean} options.dryRun - 是否为试运行模式
 */
async function autoUpload(options = {}) {
  const {
    channel = CONFIG.channel,
    distDir = UploadUtils.getBuildDirectory(),
    dryRun = false
  } = options

  console.log('🚀 开始自动上传流程...')
  console.log(`📦 版本: ${CONFIG.version}`)
  console.log(`🏷️  渠道: ${channel}`)
  console.log(`📁 构建目录: ${distDir}`)
  console.log(`🧪 试运行模式: ${dryRun ? '是' : '否'}`)

  try {
    // 1. 环境检查
    UploadUtils.checkEnvironmentVariables()
    UploadUtils.checkBuildDirectory(distDir)

    // 2. 扫描构建产物
    const updateFiles = await scanBuildArtifacts(distDir)

    if (updateFiles.length === 0) {
      console.log('⚠️  没有找到需要上传的文件')
      return { success: false, message: '没有找到需要上传的文件' }
    }

    if (dryRun) {
      console.log('\n🧪 试运行模式 - 不会实际上传文件')
      displayUploadSummary(updateFiles.length, updateFiles)
      return { success: true, message: '试运行完成', files: updateFiles }
    }

    // 3. 初始化 COS SDK
    const cos = await UploadUtils.initializeCOS()

    // 4. 上传文件
    const uploadCount = await uploadFiles(cos, updateFiles)

    // 5. 显示上传摘要
    displayUploadSummary(uploadCount, updateFiles)

    return {
      success: true,
      message: `成功上传 ${uploadCount} 个文件`,
      uploadCount,
      files: updateFiles
    }
  } catch (error) {
    console.error('\n❌ 上传失败:', error.message)

    if (error.code) {
      console.error(`错误代码: ${error.code}`)
    }
    if (error.statusCode) {
      console.error(`HTTP 状态码: ${error.statusCode}`)
    }

    return { success: false, message: error.message, error }
  }
}

// 如果直接运行脚本，执行上传
if (import.meta.url === `file://${process.argv[1].replace(/\\/g, '/')}` ||
    import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/')) ||
    process.argv[1].endsWith('auto-upload.js')) {
  console.log('🚀 开始构建产物上传...')
  console.log(`📦 版本: ${CONFIG.version}`)
  console.log(`🏷️  渠道: ${CONFIG.channel}`)

  const result = await autoUpload()
  if (!result.success) {
    process.exit(1)
  }
}

// 导出方法供其他模块使用
export { autoUpload, UploadUtils, CONFIG, PLATFORM_CONFIG }