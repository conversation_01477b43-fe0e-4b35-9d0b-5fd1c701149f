import React, { PropsWithChildren, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { isEqual } from 'lodash'

import { cacheManager } from '@/libs/cache/cache-manager'
import { EditorState } from '@/libs/cache/parts/editor.cache'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { AUTO_SAVE_INTERVAL } from '@/modules/video-editor/constants'
import { forceCacheResources } from '@/modules/video-editor/utils/track-helper'

import { AutoSaverContext } from './auto-saver.context'

import { AutosaveStatus } from './autosave-status'
import { PageLockedMask } from './page-locked-mask'
import { CloudLoadErrorDialog } from './cloud-load-error-dialog'
import { AutosaveRecoveryDialog } from './autosave-recovery-dialog'

type AutoSaverProviderProps = PropsWithChildren<{
  /**
   * 项目ID
   */
  scriptId: string
}>

/**
 * 自动保存核心逻辑 Hook
 */
function useAutosave(
  projectId: string,
  state: EditorState,
  options: {
    interval?: number
    onLoad?: (data: EditorState) => void
    onSave?: () => void
    onAutosaveDetected?: (timestamp: number) => void
    onCloudLoadStart?: () => void
    onCloudLoadSuccess?: () => void
    onCloudLoadError?: (error: Error) => void
  } = {},
) {
  const {
    interval = AUTO_SAVE_INTERVAL,
    onLoad,
    onSave,
    onAutosaveDetected,
    onCloudLoadStart,
    onCloudLoadSuccess,
    onCloudLoadError
  } = options

  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const lastSavedStateRef = useRef<string>('')
  const autoSaveCheckedRef = useRef(false)

  // 检查现有自动保存，仅在挂载时执行一次
  useEffect(() => {
    const checkForAutosave = async () => {
      if (autoSaveCheckedRef.current) return

      try {
        // 开始云端加载
        if (onCloudLoadStart) onCloudLoadStart()

        const stateFromCloud = await cacheManager.projectState.loadProjectState(projectId, true)

        // 云端加载成功
        onCloudLoadSuccess?.()

        if (stateFromCloud) {
          onLoad?.(stateFromCloud)
        }

        const autoSaved = await cacheManager.projectState.hasAutosave(projectId)

        if (autoSaved && onAutosaveDetected) {
          const sameAsManual = stateFromCloud && isEqual(stateFromCloud.tracks, autoSaved.editorState.tracks)

          if (!sameAsManual) {
            onAutosaveDetected(autoSaved.timestamp)
          }
        }
      } catch (error) {
        console.error('Failed to check for autosave:', error)

        // 云端加载失败
        if (onCloudLoadError) onCloudLoadError(error)
      } finally {
        autoSaveCheckedRef.current = true
      }
    }

    void checkForAutosave()
  }, [projectId, onCloudLoadStart, onCloudLoadSuccess, onCloudLoadError, onAutosaveDetected, onLoad])

  // 设置自动保存定时器
  useEffect(() => {
    // 如果项目ID无效，不启动自动保存
    if (!projectId) return

    const saveIfChanged = async () => {
      const currentStateString = JSON.stringify(state)

      // 只有状态发生变化时才保存
      if (currentStateString !== lastSavedStateRef.current) {
        try {
          await cacheManager.projectState.saveProjectState(projectId, state)
          lastSavedStateRef.current = currentStateString
          if (onSave) onSave()
        } catch (error) {
          console.error('Autosave failed:', error)
        }
      }
    }

    // 设置自动保存间隔
    timerRef.current = setInterval(saveIfChanged, interval)

    // 清理定时器
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [projectId, state, interval, onSave])

  // 手动保存状态函数
  const saveState = useCallback(async () => {
    try {
      await cacheManager.projectState.saveProjectState(projectId, state, true)
      lastSavedStateRef.current = JSON.stringify(state)
      if (onSave) onSave()
      return true
    } catch (error) {
      console.error('Manual save failed:', error)
      return false
    }
  }, [projectId, state, onSave])

  // 手动加载状态函数
  const loadState = async () => {
    try {
      const loadedState = await cacheManager.projectState.loadProjectState(projectId)
      if (loadedState && onLoad) {
        onLoad(loadedState)
      }
      return loadedState
    } catch (error) {
      console.error('Load failed:', error)
      return null
    }
  }

  return {
    saveState,
    loadState,
  }
}

export const AutoSaverProvider: React.FC<AutoSaverProviderProps> = ({
  children,
  scriptId,
}) => {
  const {
    tracks, aspectRatio, getPlayerDimensions, durationInFrames, videoPlayer: { fps },
    setTracksDirectly, setAspectRatio, updatePlayerDimensions
  } = useEditorContext()

  const editorState = useMemo(() => ({
    tracks,
    aspectRatio: aspectRatio,
    playerDimensions: getPlayerDimensions(),
    playerMetadata: {
      width: getPlayerDimensions().playerWidth,
      height: getPlayerDimensions().playerHeight,
      fps,
      durationInFrames
    }
  }), [tracks, aspectRatio, getPlayerDimensions, fps, durationInFrames])

  // 自动保存状态
  const [isSaving, setIsSaving] = useState(false)
  const [showRecoveryDialog, setShowRecoveryDialog] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState<number | null>(null)
  const [autosaveTimestamp, setAutosaveTimestamp] = useState<number | null>(null)
  const [cloudLoadState, setCloudLoadState] = useState<{
    isLoading: boolean
    hasError: boolean
    error?: string
    showErrorDialog: boolean
  }>({
    isLoading: false,
    hasError: false,
    showErrorDialog: false
  })

  // 云端加载回调函数
  const handleLoad = useCallback(async (loadedState: EditorState) => {
    if (loadedState) {
      const { tracks, aspectRatio, playerDimensions } = loadedState
      if (tracks) {
        setTracksDirectly(await forceCacheResources(tracks || []))
      }

      if (aspectRatio) setAspectRatio(aspectRatio)
      if (playerDimensions) {
        updatePlayerDimensions(
          playerDimensions.playerWidth,
          playerDimensions.playerHeight
        )
      }
    }
  }, [])

  const handleCloudLoadStart = useCallback(() => {
    setCloudLoadState(prev => ({ ...prev, isLoading: true, hasError: false }))
  }, [])

  const handleCloudLoadSuccess = useCallback(() => {
    setCloudLoadState(prev => ({ ...prev, isLoading: false }))
  }, [])

  const handleCloudLoadError = useCallback((error: Error) => {
    console.error('[AutoSaverProvider] 云端状态加载失败:', error)

    if (error.message === 'CLOUD_LOAD_FAILED') {
      // 云端加载失败，显示错误对话框并锁定编辑器
      setCloudLoadState({
        isLoading: false,
        hasError: true,
        error: '网络连接失败或服务器错误',
        showErrorDialog: true
      })
    } else {
      // 其他错误，静默处理
      setCloudLoadState(prev => ({ ...prev, isLoading: false }))
    }
  }, [])

  // 实现自动保存逻辑
  const { saveState, loadState } = useAutosave(scriptId, editorState, {
    interval: AUTO_SAVE_INTERVAL,
    onSave: () => {
      setIsSaving(false)
      setLastSaveTime(Date.now())
    },
    onLoad: handleLoad,
    onAutosaveDetected: timestamp => {
      // 只在初始加载时显示恢复对话框，而不是在活动会话期间
      // if (!initialLoadComplete) {
      setAutosaveTimestamp(timestamp)
      setShowRecoveryDialog(true)
      // }
    },
    onCloudLoadStart: handleCloudLoadStart,
    onCloudLoadSuccess: handleCloudLoadSuccess,
    onCloudLoadError: handleCloudLoadError
  })

  // 处理恢复对话框操作
  const handleRecoverAutosave = async () => {
    const loadedState = await loadState()
    console.debug('loadedState', loadedState)
    setShowRecoveryDialog(false)
  }

  const handleDiscardAutosave = () => {
    setShowRecoveryDialog(false)
  }

  // 重试云端加载处理函数
  const handleRetryCloudLoad = () => {
    setCloudLoadState(prev => ({ ...prev, showErrorDialog: false }))

    // 重新触发加载
    const loadCloudState = async () => {
      try {
        setCloudLoadState(prev => ({ ...prev, isLoading: true, hasError: false }))

        const cloudState = await cacheManager.projectState.loadProjectState(scriptId, true)

        if (cloudState?.tracks) {
          setTracksDirectly(await forceCacheResources(cloudState.tracks))
          console.log('[AutoSaverProvider] 云端状态重新加载成功')
        }

        setCloudLoadState(prev => ({ ...prev, isLoading: false }))
      } catch (error) {
        console.error('[AutoSaverProvider] 云端状态重新加载失败:', error)
        setCloudLoadState({
          isLoading: false,
          hasError: true,
          error: '网络连接失败或服务器错误',
          showErrorDialog: true
        })
      }
    }

    void loadCloudState()
  }

  // 手动保存函数，用于键盘快捷键或保存按钮
  const saveProject = useCallback(async () => {
    setIsSaving(true)
    await saveState()
  }, [saveState])

  // 设置键盘快捷键进行手动保存 (Ctrl+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        saveProject()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [saveProject])

  return (
    <AutoSaverContext.Provider
      value={{
        isSaving,
        lastSaveTime,
        saveProject,
      }}
    >
      {children}

      {/* 自动保存状态指示器 */}
      <AutosaveStatus
        isSaving={isSaving}
        lastSaveTime={lastSaveTime}
      />

      {/* 自动保存恢复对话框 */}
      {autosaveTimestamp && (
        <AutosaveRecoveryDialog
          open={showRecoveryDialog}
          projectId={scriptId}
          timestamp={autosaveTimestamp}
          onRecover={handleRecoverAutosave}
          onDiscard={handleDiscardAutosave}
          onClose={() => setShowRecoveryDialog(false)}
        />
      )}

      {/* 云端加载错误对话框 */}
      <CloudLoadErrorDialog
        open={cloudLoadState.showErrorDialog}
        onRetry={handleRetryCloudLoad}
        onClose={() => setCloudLoadState(prev => ({ ...prev, hasError: false, showErrorDialog: false }))}
        error={cloudLoadState.error}
      />

      {/* 编辑器锁定遮罩 */}
      <PageLockedMask
        isLoading={cloudLoadState.isLoading}
        hasError={cloudLoadState.hasError && !cloudLoadState.showErrorDialog}
        message={cloudLoadState.error}
      />
    </AutoSaverContext.Provider>
  )
}
