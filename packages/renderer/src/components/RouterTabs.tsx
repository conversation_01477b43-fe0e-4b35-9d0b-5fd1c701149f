import React, { useMemo } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/components/lib/utils'

export interface RouterTabItem {
  value: string
  label: string
  path: string
  icon?: React.ReactNode
}

interface RouterTabsProps {
  tabs: RouterTabItem[]
  basePath?: string
  className?: string
  tabsListClassName?: string
  tabsTriggerClassName?: string
}

/**
 * RouterTabs 组件 - 基于路由的选项卡导航
 *
 * 功能特性：
 * - 复用现有的 UI 选项卡组件
 * - 与 React Router 集成，支持路由导航
 * - 活动选项卡自动同步当前路由
 * - 流畅的过渡动画
 * - TypeScript 类型安全
 * - 高性能，避免不必要的重新渲染
 */
export const RouterTabs: React.FC<RouterTabsProps> = ({
  tabs,
  basePath = '',
  className,
  tabsListClassName,
  tabsTriggerClassName,
}) => {
  const location = useLocation()
  const navigate = useNavigate()

  // 计算当前活动的选项卡值
  const activeTab = useMemo(() => {
    const currentPath = location.pathname

    // 如果有 basePath，先移除它
    const relativePath = basePath
      ? currentPath.replace(basePath, '').replace(/^\//, '')
      : currentPath.split('/').pop() || ''

    // 查找匹配的选项卡
    const matchedTab = tabs.find(tab => {
      const tabPath = tab.path.replace(/^\//, '')
      return tabPath === relativePath || currentPath.endsWith(tab.path)
    })

    return matchedTab?.value || tabs[0]?.value || ''
  }, [location.pathname, basePath, tabs])

  // 处理选项卡切换
  const handleTabChange = (value: string) => {
    const targetTab = tabs.find(tab => tab.value === value)
    if (targetTab) {
      const fullPath = basePath ? `${basePath}/${targetTab.path}` : targetTab.path
      navigate(fullPath.replace(/\/+/g, '/')) // 清理多余的斜杠
    }
  }

  return (
    <Tabs
      value={activeTab}
      onValueChange={handleTabChange}
      className={cn('w-full bg-background rounded-lg', className)}
    >
      <TabsList
        className={cn(
          'inline-flex items-center justify-start bg-transparent gap-2 p-2 h-auto',
          tabsListClassName
        )}
      >
        {tabs.map(tab => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            className={cn(
              'relative inline-flex items-center justify-center rounded-lg whitespace-nowrap',
              'px-4 py-2 text-sm font-medium transition-all duration-200',
              'text-gray-200',
              'hover:text-gray-200',
              'hover:bg-gray-800/50',
              'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
              'disabled:pointer-events-none disabled:opacity-50',
              // 活动状态样式 - 底部指示器
              'data-[state=active]:text-white ',
              'data-[state=active]:bg-neutral-800 ',
              tabsTriggerClassName
            )}
          >
            {tab.icon && (
              <span className="mr-2 h-4 w-4 ">
                {tab.icon}
              </span>
            )}
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  )
}