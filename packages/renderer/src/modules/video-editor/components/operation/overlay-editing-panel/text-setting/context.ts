import React from 'react'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import opentype from 'opentype.js'
import { DeepPartial } from '@/types/utils'

type TextSettingContextValues = {
  textOverlay: TextOverlay
  requestUpdateText(
    update: DeepPartial<TextOverlay>,
    commit?: boolean,
    font?: opentype.Font
  ): void
}

export const TextSettingContext = React.createContext<TextSettingContextValues>(null as any)

export const useTextSettingContext = () => React.useContext(TextSettingContext)
