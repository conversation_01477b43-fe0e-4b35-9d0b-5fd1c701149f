import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'
import { BaseResourceQueryParams, StyledTextResource } from '@/types/resources'
import { useInfiniteQuery } from '../useInfiniteQuery'

/**
 * 花体字样式查询Hook
 * 专门用于获取花体字样式预设，使用正确的FontStyleResource类型
 */
export const useInfiniteQueryFontStyleList = (params: BaseResourceQueryParams) => {
  return useInfiniteQuery<StyledTextResource.StyledText>(
    [QUERY_KEYS.FONT_STYLE_LIST],
    ResourceModule.fontStyle.list,
    params,
    {
      pageSize: params.pageSize || 50,
    }
  )
}

