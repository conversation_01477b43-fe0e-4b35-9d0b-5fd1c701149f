# 错误处理模块使用指南

## 简介

错误处理模块提供了统一的方式来处理、显示和上报各种类型的错误。该模块支持多种错误类型，包括 HTTP 错误、业务错误、运行时错误和 IPC 错误等。

## 功能特点

- 统一的错误处理接口
- 自动错误分类和格式化
- 集成 Toast 提示
- 错误上报功能
- 全局错误捕获
- React 错误边界
- 支持自定义错误处理选项

## 使用方法

### 1. 使用 ErrorProvider

在应用的根组件中包裹 ErrorProvider：

```tsx
import { ErrorProvider } from '@/contexts/ErrorContext';

function App() {
  return (
    <ErrorProvider>
      <YourApp />
    </ErrorProvider>
  );
}
```

### 2. 使用 ErrorBoundary

在组件树中使用 ErrorBoundary 捕获渲染错误：

```tsx
import { ErrorBoundary } from '@/contexts/ErrorContext';

function YourComponent() {
  return (
    <ErrorBoundary>
      <ComponentThatMightError />
    </ErrorBoundary>
  );
}
```

自定义错误回退 UI：

```tsx
import { ErrorBoundary } from '@/contexts/ErrorContext';
import { ErrorInfo } from '@/types/error';

function YourComponent() {
  return (
    <ErrorBoundary
      fallback={(error: ErrorInfo) => (
        <div>
          <h3>出错了</h3>
          <p>{error.message}</p>
          <button onClick={() => window.location.reload()}>刷新页面</button>
        </div>
      )}
    >
      <ComponentThatMightError />
    </ErrorBoundary>
  );
}
```

### 3. 使用 useError Hook

在函数组件中使用 useError Hook 处理错误：

```tsx
import { useError } from '@/contexts/ErrorContext';
import { ErrorType } from '@/types/error';

function YourComponent() {
  const { handleError, createError } = useError();

  const handleClick = () => {
    try {
      // 可能会抛出错误的代码
      throw new Error('发生了一个错误');
    } catch (error) {
      // 处理错误
      handleError(error);
    }
  };

  const handleCustomError = () => {
    // 创建自定义错误
    createError(
      '自定义错误消息',
      'CUSTOM_ERROR_CODE',
      ErrorType.VALIDATION,
      {
        showToast: true,
        report: true,
        context: { additionalInfo: 'Some context' }
      }
    );
  };

  return (
    <div>
      <button onClick={handleClick}>触发错误</button>
      <button onClick={handleCustomError}>触发自定义错误</button>
    </div>
  );
}
```

### 4. 直接使用 errorService

在非 React 代码中，可以直接使用 errorService：

```ts
import errorService from '@/services/ErrorService';

try {
  // 可能会抛出错误的代码
} catch (error) {
  errorService.handleError(error, {
    showToast: true,
    report: true
  });
}
```

### 5. 处理特定类型的错误

处理 HTTP 错误：

```ts
import { AxiosError } from 'axios';
import errorService from '@/services/ErrorService';

async function fetchData() {
  try {
    const response = await axios.get('/api/data');
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      errorService.handleHttpError(error);
    } else {
      errorService.handleError(error);
    }
  }
}
```

处理业务错误：

```ts
import errorService from '@/services/ErrorService';

function processApiResponse(response) {
  if (response.code !== 0) {
    errorService.handleBusinessError(response.code);
    return null;
  }
  return response.data;
}
```

### 6. 自定义错误处理选项

```ts
import errorService from '@/services/ErrorService';
import { ErrorSeverity } from '@/types/error';

errorService.handleError(error, {
  // 是否显示 Toast 提示
  showToast: true,
  // 是否上报错误
  report: true,
  // 错误严重程度
  severity: ErrorSeverity.WARNING,
  // 错误上下文
  context: {
    userId: '123',
    action: 'save_document',
    additionalInfo: '...'
  }
});
```

## 错误类型

模块支持以下错误类型：

- `ErrorType.NETWORK`: 网络错误
- `ErrorType.HTTP`: HTTP 错误
- `ErrorType.BUSINESS`: 业务错误
- `ErrorType.VALIDATION`: 验证错误
- `ErrorType.PERMISSION`: 权限错误
- `ErrorType.RESOURCE`: 资源错误
- `ErrorType.RUNTIME`: 运行时错误
- `ErrorType.UNKNOWN`: 未知错误
- `ErrorType.IPC`: IPC 错误

## 错误严重程度

可以设置以下错误严重程度：

- `ErrorSeverity.FATAL`: 致命错误
- `ErrorSeverity.ERROR`: 错误
- `ErrorSeverity.WARNING`: 警告
- `ErrorSeverity.INFO`: 信息

## 最佳实践

1. 使用 ErrorBoundary 包裹关键组件，防止整个应用因渲染错误而崩溃
2. 在异步操作中使用 try-catch 并调用 handleError
3. 为不同类型的错误设置适当的严重程度
4. 在错误上下文中提供有用的信息，以便更好地诊断问题
5. 确保错误上报数据不包含敏感信息 
