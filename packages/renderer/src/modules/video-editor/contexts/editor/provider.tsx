import React, { ReactNode, useEffect } from 'react'
import { EditorContext } from './context'
import { useOverlays } from './useOverlays'
import { useVideoPlayer } from './useVideoPlayer'
import { useAspectRatio } from './useAspectRatio'
import { useCompositionDuration } from './useCompositionDuration'
import { useHistory } from './useHistory'
import { cleanupPlugins, initializePlugins } from '@/modules/video-editor/resource-plugin-system'

export const EditorProvider: React.FC<{
  scriptId: string
  projectId: string
  children: ReactNode
}> = ({ children, scriptId, projectId }) => {
  const overlaysHook = useOverlays()

  const videoPlayerHook = useVideoPlayer()

  const aspectRatioHook = useAspectRatio()

  const { tracks, setTracksDirectly } = overlaysHook

  const compositionDuration = useCompositionDuration(tracks)

  const historyHook = useHistory(tracks, setTracksDirectly)

  useEffect(() => {
    initializePlugins()

    return () => {
      cleanupPlugins()
    }
  }, [])

  return (
    <EditorContext.Provider
      value={{
        scriptId,
        projectId,

        ...overlaysHook,
        ...aspectRatioHook,
        ...historyHook,
        ...compositionDuration,
        videoPlayer: videoPlayerHook,
        history: historyHook,

        renderMedia: () => { },
        state: {},
      }}
    >
      {children}
    </EditorContext.Provider>
  )
}

