import React from 'react'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'

export const GlobalSetting = () => {
  return (
    <div className="text-white w-full">
      {/* 标题 */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-medium text-blue-400">全局设置</h3>

      </div>

      {/* 混搭合成模式 */}
      <div className="mb-4">
        <p className="text-xs text-gray-400 mb-2">混搭合成模式:</p>
        <RadioGroup defaultValue="image-first" className="space-y-2">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="image-first" id="image-first" />
            <Label htmlFor="image-first" className="text-xs cursor-pointer">按画面优先</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="voice-first" id="voice-first" />
            <Label htmlFor="voice-first" className="text-xs cursor-pointer">按口播优先</Label>
          </div>
        </RadioGroup>
      </div>

      {/* 分段式口播混搭规则 */}
      <div className="mb-4">
        <p className="text-xs text-gray-400 mb-2">分段式口播混搭规则:</p>
        <RadioGroup defaultValue="random" className="space-y-2">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="random" id="random" />
            <Label htmlFor="random" className="text-xs cursor-pointer">随机混搭</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="popular" id="popular" />
            <Label htmlFor="popular" className="text-xs cursor-pointer">按热度倾向混搭</Label>
          </div>
        </RadioGroup>
      </div>

      {/* 分段素材智能替换 */}
      <div className="mb-4">
        <div className="flex justify-between items-center">
          <p className="text-xs text-gray-400">分段素材智能替换:</p>
          <Switch id="auto-replace" />
        </div>
      </div>

      {/* 描述信息 */}
      <p className="text-xs text-gray-400 mt-2">
        开启后，系统将分析素材，如分段时长&lt;5s，放入10s的素材，将从中筛选出最佳时段。更大可能展现从AI生成的素材。
      </p>
    </div>
  )
}
