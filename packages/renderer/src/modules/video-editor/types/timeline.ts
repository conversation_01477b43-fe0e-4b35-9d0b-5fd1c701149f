import { BaseOverlay, Overlay } from '@clipnest/remotion-shared/types'

// Type for the ghost element's visual properties
export interface GhostElement extends Pick<BaseOverlay, 'from' | 'durationInFrames'> {
  row: number
  overlay?: Overlay
  invalid?: boolean
}

export enum TrackType {
  /**
   * 分镜轨道
   */
  STORYBOARD = 'storyboard',

  /**
   * 视频轨道
   */
  VIDEO = 'video',

  /**
   * 口播轨道
   */
  NARRATION = 'narration',

  /**
   * 声音轨道
   */
  SOUND = 'sound',

  /**
   * 文字轨道
   */
  TEXT = 'text',

  /**
   * 图片轨道.
   */
  IMAGE = 'image',

  /**
   * 混合轨道. 可以存放除分镜外的任意类型
   */
  MIXED = 'mixed',

  // /**
  //  * 占位符. 预留设计, 作用暂定
  //  */
  // EMPTY = 'EMPTY',
}

export type Track = {
  /**
   * 轨道类型. 不同类型的轨道接受不同类型的 overlay
   */
  type: TrackType

  /**
   * 轨道中包含的 overlay
   */
  overlays: Overlay[]

  /**
   * 是否为全局轨道. 为假值时则说明是混剪轨道, 将参与混剪, 且其内的 Overlay 受到长度限制
   */
  isGlobalTrack?: boolean
}

export interface WaveformData {
  peaks: number[]
  length: number
}

export type IndexableTrack = Track & { index: number }

export type IndexableOverlay = Overlay & { index: number }

