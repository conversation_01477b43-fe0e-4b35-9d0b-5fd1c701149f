import React from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { cn } from '@/components/lib/utils'

export interface WithConfirmProps {
  /**
   * 确认对话框的标题
   */
  title: string
  /**
   * 确认对话框的描述内容
   */
  description: string
  /**
   * 确认按钮的文本
   */
  confirmText?: string
  /**
   * 取消按钮的文本
   */
  cancelText?: string
  /**
   * 确认按钮的变体样式
   */
  confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  /**
   * 点击确认按钮时的回调函数
   */
  onConfirm: () => void | Promise<void>
  /**
   * 点击取消按钮时的回调函数（可选）
   */
  onCancel?: () => void | Promise<void>
  /**
   * 子元素（触发器）
   */
  children: React.ReactNode
  /**
   * 是否禁用确认对话框
   */
  disabled?: boolean
  /**
   * 自定义类名
   */
  className?: string
  asChild?: boolean
}

/**
 * 通用确认对话框包装组件
 *
 * @example
 * ```tsx
 * <WithConfirm
 *   title="警告"
 *   description="重置后所有更改将丢失，确定要重置吗？"
 *   onConfirm={resetOverlays}
 * >
 *   <Button>
 *     <RotateCcw className="size-3.5" />
 *   </Button>
 * </WithConfirm>
 * ```
 */
export function WithConfirm({
  title,
  description,
  confirmText = '确认',
  cancelText = '取消',
  confirmVariant = 'default',
  onConfirm,
  onCancel,
  children,
  disabled = false,
  className,
  asChild = false,
}: WithConfirmProps) {
  const [open, setOpen] = React.useState(false)
  const [isLoading, setIsLoading] = React.useState(false)

  const handleConfirm = async () => {
    try {
      setIsLoading(true)
      await onConfirm()
      setOpen(false)
    } catch (error) {
      console.error('Confirm action failed:', error)
      // 可以在这里添加错误处理，比如显示 toast
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = async () => {
    try {
      if (onCancel) {
        await onCancel()
      }
      setOpen(false)
    } catch (error) {
      console.error('Cancel action failed:', error)
    }
  }

  if (disabled) {
    return <>{children}</>
  }

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild={asChild}>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent className={className}>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={isLoading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className={cn(
              confirmVariant === 'destructive' && 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
              confirmVariant === 'outline' && 'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',
              confirmVariant === 'secondary' && 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
              confirmVariant === 'ghost' && 'hover:bg-accent hover:text-accent-foreground',
              confirmVariant === 'link' && 'text-primary underline-offset-4 hover:underline'
            )}
          >
            {isLoading ? '处理中...' : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
