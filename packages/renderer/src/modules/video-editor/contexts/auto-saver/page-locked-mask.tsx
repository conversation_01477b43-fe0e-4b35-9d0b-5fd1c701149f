import React from 'react'
import { Loader2, AlertTriangle } from 'lucide-react'

interface EditorLockOverlayProps {
  isLoading?: boolean
  hasError?: boolean
  message?: string
}

/**
 * 编辑器锁定遮罩层
 * 在云端数据加载过程中或加载失败时显示，阻止用户操作
 */
export const PageLockedMask: React.FC<EditorLockOverlayProps> = ({
  isLoading = false,
  hasError = false,
  message
}) => {
  if (!isLoading && !hasError) {
    return null
  }

  return (
    <div className="fixed inset-0 z-[9999] bg-background/80 backdrop-blur-sm flex items-center justify-center">
      <div className="bg-card border rounded-lg p-6 shadow-lg max-w-md w-full mx-4">
        <div className="flex flex-col items-center text-center space-y-4">
          {isLoading ? (
            <>
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <div>
                <h3 className="text-lg font-semibold">正在加载编辑器状态</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  正在从云端获取您的编辑器数据，请稍候...
                </p>
              </div>
            </>
          ) : hasError ? (
            <>
              <AlertTriangle className="h-8 w-8 text-destructive" />
              <div>
                <h3 className="text-lg font-semibold text-destructive">加载失败</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {message || '无法加载编辑器状态，编辑器已被锁定'}
                </p>
              </div>
            </>
          ) : null}
        </div>
      </div>
    </div>
  )
}
