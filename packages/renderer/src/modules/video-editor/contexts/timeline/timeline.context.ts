import React, { createContext, useContext } from 'react'

import { TimelineZoomHook } from './useTimelineZoom'
import { TimelineClipboard } from './useTimelineClipboard'
import { TimelineTracksLayout } from './useTimelineTracksLayout'
import { TimelineOverlayActivation } from './useTimelineOverlayActivation'

/**
 * Context interface for managing timeline state and interactions.
 * @interface TimelineContextType
 */
interface TimelineContextType extends
  TimelineZoomHook,
  TimelineOverlayActivation
{
  layout: TimelineTracksLayout
  clipboard: TimelineClipboard

  isContextMenuOpen: boolean,
  setIsContextMenuOpen(v: boolean): void

  /**
   * 指示当前鼠标指向的时间点
   */
  mouseOnCurrentFrame: number | null
  setMouseOnCurrentFrame(frame: number | null): void

  /** Reference to the timeline grid DOM element */
  timelineGridRef: React.RefObject<HTMLDivElement | null>
}

/**
 * Context for sharing timeline state and functionality across components.
 */
export const TimelineContext = createContext<TimelineContextType>({} as any)

export const useTimelineContext = () => {
  const context = useContext(TimelineContext)
  if (!context) {
    throw new Error('useTimelineContext must be used within a TimelineProvider')
  }
  return context
}

type UseTimelineReturn = TimelineContextType

export const useTimeline = (): UseTimelineReturn => {
  const timelineContext = useContext(TimelineContext)
  if (!timelineContext) throw new Error('useTimeline must be used within a TimelineProvider')

  return {
    ...timelineContext,
  }
}
