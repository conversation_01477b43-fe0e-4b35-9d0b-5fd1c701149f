import { FileDownloaderIPCClient, FileSelectOptions, FolderSelectOptions } from '@app/shared/types/ipc/file-uploader.js'
import { Injectable, Inject } from '@nestjs/common'
import { RequestService } from '../global/request.service.js'
import fs from 'fs/promises'
import { BrowserWindow, dialog, OpenDialogOptions } from 'electron'
import { createWriteStream } from 'fs'
import { pipeline } from 'stream/promises'

/**
 * 文件上传服务
 * 提供文件和文件夹选择功能
 */
@Injectable()
export class FileDownloaderService implements FileDownloaderIPCClient {

  constructor(@Inject(RequestService) private readonly requestService: RequestService) {}

  public async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  public async selectFile({ multiple, ...options }: FileSelectOptions = {}): Promise<string[] | null> {
    const window = BrowserWindow.getFocusedWindow()
    if (!window) return null
    const properties: OpenDialogOptions['properties'] = ['openFile']
    if (multiple) properties.push('multiSelections')
    const result = await dialog.showOpenDialog(window, { ...options, properties })
    return result.filePaths || null
  }

  public async selectFolder({ multiple, ...options }: FolderSelectOptions = {}): Promise<string[] | null> {
    const window = BrowserWindow.getFocusedWindow()
    if (!window) return null
    const properties: OpenDialogOptions['properties'] = ['openDirectory']
    if (multiple) properties.push('multiSelections')
    const result = await dialog.showOpenDialog(window, { ...options, properties })
    return result.filePaths || null
  }

  public async downloadFiles(files: { url: string; path: string }[]): Promise<boolean[]> {
    return Promise.all(files.map(file => this.downloadFile(file)))
  }

  private async downloadFile(file: { url: string; path: string }): Promise<boolean> {
    const url = await this.requestService.get<string>(file.url)
    const response = await fetch(url)
    const stream = createWriteStream(file.path)
    if (!response.body) return false
    await pipeline(response.body, stream)
    return true
  }
}
