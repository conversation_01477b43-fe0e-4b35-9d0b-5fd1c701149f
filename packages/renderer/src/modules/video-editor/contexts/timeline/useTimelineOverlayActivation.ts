import { useCallback } from 'react'
import { findOverlayStoryboard, getOverlayTrackIndex } from '@/modules/video-editor/utils/overlay-helper'
import { useMap } from 'usehooks-ts'
import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { useEditorContext } from '../editor/context'
import { Track, TrackType } from '@/modules/video-editor/types'

type ReadonlyMap<K, V> = Omit<Map<K, V>, 'set' | 'delete' | 'clear'>

export type TimelineOverlayActivation = {
  /**
   * 分镜视频轨道的激活状态
   * Key: 分镜序号
   * Value: 该分镜上激活的视频轨道序号
   */
  videoActivation: ReadonlyMap<number, number>

  /**
   * 分镜口播轨道的激活状态
   * Key: 分镜序号
   * Value: 该分镜上激活的口播轨道序号
   */
  narrationActivation: ReadonlyMap<number, number>

  /**
   * 通过点击 Overlay , 将其所在的轨道设置其所在分镜的激活轨道
   */
  toggleOverlaySelection: (overlay: Overlay) => void

  /**
   * 判断指定的 Overlay 是否处于激活状态. 对分镜视频轨道和分镜口播轨道生效, 其他的默认返回 `true`
   */
  getOverlayActivationState: (overlay: Overlay) => boolean
}

export const useTimelineOverlayActivation = (): TimelineOverlayActivation => {
  const { tracks } = useEditorContext()

  const [videoActivation, videoActivationActions] = useMap<number, number>(
    tracks.find(t => t.type === TrackType.STORYBOARD)
      ?.overlays
      .map((_, index) => {
        // TODO: 查找第一个存在的 Overlay, 而不是默认返回 1
        // const overlays = findOverlaysAboveStorybook(tracks, storyboard)
        return [index, 1]
      })
    || []
  )

  const initializeNarrationActivation = (tracks: Track[]) => {
    return tracks.find(t => t.type === TrackType.STORYBOARD)
      ?.overlays
      .map((_storyboard, index) => {
        // const overlays = findOverlaysAboveStorybook(tracks, storyboard as StoryboardOverlay)

        return [index, null]
      })
      .filter(([_, index]) => index !== null) as Array<[number, number]>
      || []
  }

  const [narrationActivation, narrationActivationActions] = useMap<number, number>(initializeNarrationActivation(tracks))

  const toggleOverlaySelection = useCallback<TimelineOverlayActivation['toggleOverlaySelection']>(
    overlay => {
      const storyboard = findOverlayStoryboard(tracks, overlay)
      const trackIndex = getOverlayTrackIndex(tracks, overlay.id)

      if (overlay.type === OverlayType.VIDEO && storyboard && trackIndex !== -1) {
        videoActivationActions.set(storyboard.index, trackIndex)
      }

      // 处理分镜口播轨道的激活状态切换
      if ((overlay.type === OverlayType.TEXT || overlay.type === OverlayType.SOUND) && storyboard && trackIndex !== -1) {
        // 检查当前 overlay 是否在分镜口播轨道中（混剪轨道）
        const track = tracks[trackIndex]
        if (track && track.type === TrackType.NARRATION && !track.isGlobalTrack) {
          narrationActivationActions.set(storyboard.index, trackIndex)
        }
      }
    },
    [tracks, videoActivationActions, narrationActivationActions]
  )

  const getOverlayActivationState = useCallback<TimelineOverlayActivation['getOverlayActivationState']>(
    overlay => {
      const storyboard = findOverlayStoryboard(tracks, overlay)
      const trackIndex = getOverlayTrackIndex(tracks, overlay.id)

      const track = tracks[trackIndex]
      if (!track || track.isGlobalTrack) {
        return true
      }

      // 处理分镜视频轨道的激活状态
      if (overlay.type === OverlayType.VIDEO) {
        if (track.type !== TrackType.VIDEO) return true

        if (!storyboard) return false
        return videoActivation.get(storyboard.index) === trackIndex
      }

      // 处理分镜口播轨道的激活状态
      if (overlay.type === OverlayType.TEXT || overlay.type === OverlayType.SOUND) {
        if (track.type !== TrackType.NARRATION) return true

        // 检查当前 overlay 是否在分镜口播轨道中（混剪轨道）
        if (storyboard) {
          return narrationActivation.get(storyboard.index) === trackIndex
        }
      }

      // 其他类型的 overlay 默认返回 true（始终激活）
      return true
    },
    [videoActivation, narrationActivation, tracks]
  )

  return {
    videoActivation,
    narrationActivation,
    toggleOverlaySelection,
    getOverlayActivationState,
  }
}
