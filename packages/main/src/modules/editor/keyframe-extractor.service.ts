import { Injectable } from '@nestjs/common'
import ffmpeg from 'fluent-ffmpeg'
import { join } from 'path'
import { tmpdir } from 'os'
import { promises as fs } from 'fs'

@Injectable()
export class KeyframeExtractorService {

  public async extractKeyframes(src: string) {
    try {
      // 获取视频信息
      const videoInfo = await this.getVideoInfo(src)
      const { duration, fps = 30, width, height } = videoInfo

      // 根据视频时长智能决定关键帧提取间隔
      const intervalSeconds = this.calculateFrameInterval(duration)

      // 计算需要提取的时间点
      const timePoints = this.calculateTimePoints(duration, intervalSeconds)

      // 计算缩略图尺寸
      const thumbnailSize = this.calculateThumbnailSize(width, height)

      // console.debug(`[KeyframeExtractor] 视频信息: ${width}x${height}, 缩略图尺寸: ${thumbnailSize.width}x${thumbnailSize.height}`)

      // 提取关键帧
      return this.extractFramesAtTimePoints(src, timePoints, fps, thumbnailSize)
    } catch (error: any) {
      console.error('[EditorService] 提取视频关键帧失败:', error)
      throw new Error(`提取视频关键帧失败: ${error.message}`)
    }
  }

  /**
   * 获取视频信息（时长、帧率、尺寸等）
   */
  private async getVideoInfo(src: string): Promise<{
    duration: number;
    fps?: number;
    width: number;
    height: number
  }> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(src, (err, metadata) => {
        if (err) {
          reject(new Error(`无法获取视频信息: ${err.message}`))
          return
        }

        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video')
        if (!videoStream) {
          reject(new Error('未找到视频流'))
          return
        }

        const duration = metadata.format.duration
        if (!duration) {
          reject(new Error('无法获取视频时长'))
          return
        }

        // 获取视频尺寸
        const width = videoStream.width
        const height = videoStream.height
        if (!width || !height) {
          reject(new Error('无法获取视频尺寸'))
          return
        }

        // 解析帧率
        let fps = 30 // 默认帧率
        if (videoStream.r_frame_rate) {
          const [num, den] = videoStream.r_frame_rate.split('/').map(Number)
          if (den && den !== 0) {
            fps = num / den
          }
        }

        resolve({ duration, fps, width, height })
      })
    })
  }

  /**
   * 根据视频时长计算关键帧提取间隔
   */
  private calculateFrameInterval(duration: number): number {
    if (duration <= 20) {
      return 2 // 每2秒1帧
    } else if (duration <= 100) {
      return 5 // 每5秒1帧
    } else {
      return 10 // 每10秒1帧
    }
  }

  /**
   * 计算缩略图尺寸，保持宽高比
   */
  private calculateThumbnailSize(originalWidth: number, originalHeight: number): { width: number; height: number } {
    const MAX_WIDTH = 640
    const MAX_HEIGHT = 360

    // 计算原始宽高比
    const aspectRatio = originalWidth / originalHeight

    // 根据最大宽度计算高度
    let targetWidth = Math.min(originalWidth, MAX_WIDTH)
    let targetHeight = Math.round(targetWidth / aspectRatio)

    // 如果计算出的高度超过最大高度，则根据最大高度重新计算宽度
    if (targetHeight > MAX_HEIGHT) {
      targetHeight = MAX_HEIGHT
      targetWidth = Math.round(targetHeight * aspectRatio)
    }

    // 确保尺寸为偶数（ffmpeg 要求）
    targetWidth = targetWidth % 2 === 0 ? targetWidth : targetWidth - 1
    targetHeight = targetHeight % 2 === 0 ? targetHeight : targetHeight - 1

    // 最小尺寸限制
    targetWidth = Math.max(targetWidth, 32)
    targetHeight = Math.max(targetHeight, 18)

    return { width: targetWidth, height: targetHeight }
  }

  /**
   * 计算需要提取关键帧的时间点
   */
  private calculateTimePoints(duration: number, intervalSeconds: number): number[] {
    const timePoints: number[] = []

    // 从第1秒开始，避免视频开头可能的黑屏
    for (let time = 1; time < duration; time += intervalSeconds) {
      timePoints.push(time)
    }

    // 确保至少有一帧，如果视频很短的话
    if (timePoints.length === 0 && duration > 0) {
      timePoints.push(Math.min(1, duration / 2))
    }

    return timePoints
  }

  /**
   * 在指定时间点提取视频帧
   */
  private async extractFramesAtTimePoints(
    src: string,
    timePoints: number[],
    fps: number,
    thumbnailSize: { width: number; height: number }
  ): Promise<{ frameNumber: number; dataUrl: string }[]> {
    const results: { frameNumber: number; dataUrl: string }[] = []

    // 创建临时目录
    const tempDir = join(tmpdir(), 'clipnest', `video-frames-${encodeURIComponent(src)}`)
    await fs.mkdir(tempDir, { recursive: true })

    try {
      // 批量提取所有帧
      await this.extractAllFrames(src, timePoints, tempDir, thumbnailSize)

      // 读取提取的帧并转换为 Data URL
      for (let i = 0; i < timePoints.length; i++) {
        const timePoint = timePoints[i]
        const frameNumber = Math.floor(timePoint * fps)
        const framePath = join(tempDir, `frame_${i.toString().padStart(4, '0')}.jpg`)

        try {
          // 检查文件是否存在
          await fs.access(framePath)

          // 读取图片文件并转换为 base64
          const imageBuffer = await fs.readFile(framePath)
          const dataUrl = `data:image/jpeg;base64,${imageBuffer.toString('base64')}`

          results.push({
            frameNumber,
            dataUrl
          })
        } catch (error: any) {
          console.warn(`[EditorService] 无法读取帧文件 ${framePath}:`, error.message)
          // 继续处理其他帧，不因为单个帧失败而中断整个过程
        }
      }

      return results
    } finally {
      // 清理临时文件
      await this.cleanupTempDir(tempDir)
    }
  }

  /**
   * 使用 ffmpeg 批量提取所有帧
   */
  private async extractAllFrames(
    src: string,
    timePoints: number[],
    outputDir: string,
    thumbnailSize: { width: number; height: number }
  ): Promise<void> {
    // 为每个时间点单独提取一帧
    const extractPromises = timePoints.map((timePoint, index) => {
      return this.extractSingleFrame(
        src,
        timePoint,
        join(outputDir, `frame_${index.toString().padStart(4, '0')}.jpg`),
        thumbnailSize
      )
    })

    await Promise.all(extractPromises)
  }

  /**
   * 提取单个帧
   */
  private async extractSingleFrame(
    src: string,
    timePoint: number,
    outputPath: string,
    thumbnailSize: { width: number; height: number }
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      // 构建动态缩放参数，保持宽高比
      const scaleFilter = `scale=${thumbnailSize.width}:${thumbnailSize.height}`

      // console.debug(`[KeyframeExtractor] 提取帧: ${timePoint}s, 尺寸: ${scaleFilter}`)

      ffmpeg(src)
        .seekInput(timePoint)
        .frames(1)
        .outputOptions([
          '-q:v', '10', // JPEG 质量设置 (1-31, 数字越小质量越高)
          '-vf', scaleFilter, // 动态缩放，保持宽高比
        ])
        .output(outputPath)
        .on('end', () => {
          resolve()
        })
        .on('error', err => {
          reject(new Error(`提取帧失败 (时间点: ${timePoint}s): ${err.message}`))
        })
        .run()
    })
  }

  /**
   * 清理临时目录
   */
  private async cleanupTempDir(tempDir: string): Promise<void> {
    try {
      // const files = await fs.readdir(tempDir)
      // await Promise.all(files.map(file => fs.unlink(join(tempDir, file))))
      // await fs.rmdir(tempDir)
    } catch (error: any) {
      // 忽略清理错误，避免影响主要功能
      console.warn('[EditorService] 清理临时目录时出错:', error.message)
    }
  }
}
