import React, { useState } from 'react'
import axios from 'axios'
import { useErrorHandler } from '@/contexts'
import { ErrorSeverity, ErrorType } from '@/types/error'

/**
 * 错误处理演示组件
 * 展示各种错误处理方法的使用
 */
export const ErrorHandlingDemo: React.FC = () => {
  const { handleError, handleBusinessError, createError, lastError, clearLastError } = useErrorHandler()
  const [loading, setLoading] = useState(false)

  // 触发运行时错误
  const triggerRuntimeError = () => {
    try {
      // 故意触发错误
      const obj = null
      // @ts-ignore - 故意触发错误
      const result = obj.nonExistentMethod()
      console.log(result)
    } catch (error) {
      handleError(error)
    }
  }

  // 触发 HTTP 错误
  const triggerHttpError = async () => {
    setLoading(true)
    try {
      // 请求一个不存在的 URL
      await axios.get('https://api.example.com/nonexistent')
    } catch (error) {
      handleError(error)
    } finally {
      setLoading(false)
    }
  }

  // 触发业务错误
  const triggerBusinessError = () => {
    // 模拟业务错误代码
    handleBusinessError(10001)
  }

  // 触发自定义错误
  const triggerCustomError = () => {
    createError(
      '这是一个自定义错误',
      'CUSTOM_ERROR',
      ErrorType.VALIDATION,
      {
        severity: ErrorSeverity.WARNING,
        context: {
          source: 'ErrorHandlingDemo',
          action: 'triggerCustomError',
          timestamp: new Date().toISOString()
        }
      }
    )
  }

  // 触发未处理的 Promise 错误
  const triggerUnhandledPromiseError = () => {
    // 故意不使用 try-catch 来触发未处理的 Promise 错误
    new Promise((_, reject) => {
      reject(new Error('未处理的 Promise 错误'))
    })
  }

  // 测试后端错误消息显示
  const testBackendErrorMessage = async () => {
    setLoading(true)
    try {
      // 模拟后端返回的错误格式
      const mockResponse = {
        code: 2001001006,
        data: null,
        msg: '发布时间必须在2小时以后'
      }

      // 手动触发响应拦截器的逻辑
      if (mockResponse.code !== 0 && mockResponse.code !== 200) {
        handleBusinessError(mockResponse.code, {
          customMessage: mockResponse.msg
        })
      }
    } catch (error) {
      handleError(error)
    } finally {
      setLoading(false)
    }
  }

  // 渲染组件崩溃
  const CrashingComponent = () => {
    throw new Error('组件渲染崩溃')
  }

  return (
    <div className="p-6 max-w-3xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">错误处理演示</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <button
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          onClick={triggerRuntimeError}
        >
          触发运行时错误
        </button>

        <button
          className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
          onClick={triggerHttpError}
          disabled={loading}
        >
          {loading ? '请求中...' : '触发 HTTP 错误'}
        </button>

        <button
          className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          onClick={triggerBusinessError}
        >
          触发业务错误
        </button>

        <button
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          onClick={triggerCustomError}
        >
          触发自定义错误
        </button>

        <button
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
          onClick={triggerUnhandledPromiseError}
        >
          触发未处理的 Promise 错误
        </button>

        <button
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          onClick={testBackendErrorMessage}
          disabled={loading}
        >
          {loading ? '测试中...' : '测试后端错误消息'}
        </button>
      </div>

      {lastError && (
        <div className="mt-6 p-4 border border-red-300 bg-red-50 rounded">
          <div className="flex justify-between items-start">
            <h3 className="text-lg font-semibold text-red-800">最近的错误</h3>
            <button
              className="text-red-500 hover:text-red-700"
              onClick={clearLastError}
            >
              清除
            </button>
          </div>
          <p className="mt-2"><strong>消息:</strong> {lastError.message}</p>
          <p><strong>代码:</strong> {lastError.code}</p>
          <p><strong>类型:</strong> {lastError.type}</p>
          <p><strong>严重程度:</strong> {lastError.severity}</p>
          <p><strong>时间:</strong> {new Date(lastError.timestamp).toLocaleString()}</p>
          {lastError.stack && (
            <details className="mt-2">
              <summary className="cursor-pointer text-red-700">查看堆栈</summary>
              <pre className="mt-2 p-2 bg-gray-100 text-xs overflow-auto max-h-40">
                {lastError.stack}
              </pre>
            </details>
          )}
        </div>
      )}

      <div className="mt-8 p-4 border border-gray-300 rounded">
        <h2 className="text-xl font-semibold mb-4">错误边界测试</h2>
        <p className="mb-4">点击下面的按钮将渲染一个会崩溃的组件，但错误边界会捕获错误并防止整个应用崩溃。</p>

        <button
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          onClick={() => {
            // 使用 React 状态来触发渲染崩溃组件
            setLoading(true)
          }}
        >
          渲染崩溃组件
        </button>

        {loading && <CrashingComponent />}
      </div>
    </div>
  )
}
