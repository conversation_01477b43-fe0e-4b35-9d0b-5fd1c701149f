import { CaptionStyles } from '@clipnest/remotion-shared/types'

export const captionTemplates: Record<
  string,
  { name: string, styles: CaptionStyles, preview: string, isPro?: boolean }
> = {
  classic: {
    name: 'Classic',
    preview: 'Clean & Professional',
    styles: {
      fontFamily: 'Inter, sans-serif',
      fontSize: '2.2rem',
      lineHeight: 1.4,
      textAlign: 'center',
      color: '#FFFFFF',
      textShadow: '2px 2px 4px rgba(0,0,0,0.6), 0 0 20px rgba(0,0,0,0.3)',
      padding: '12px',
      fontWeight: 500,
      letterSpacing: '0.01em',
      highlightStyle: {
        backgroundColor: 'rgba(59, 130, 246, 0.92)',
        color: '#FFFFFF',
        scale: 1.06,
        fontWeight: 600,
        textShadow: '2px 2px 4px rgba(0,0,0,0.4)',
        borderRadius: '6px',
        padding: '2px 8px',
      },
    },
  },
  hustle: {
    name: 'Hust<PERSON>',
    preview: 'Bold & Dynamic',
    isPro: true,
    styles: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: '2.2rem',
      lineHeight: 1.3,
      textAlign: 'center',
      color: '#FFFFFF',
      fontWeight: 800,
      letterSpacing: '-0.02em',
      textShadow: '3px 3px 0 rgba(0,0,0,0.8)',
      highlightStyle: {
        backgroundColor: '#FF3B3B',
        color: '#FFFFFF',
        scale: 1.08,
        fontWeight: 800,
        textShadow: '3px 3px 0 rgba(0,0,0,0.8)',
        borderRadius: '2px',
        padding: '4px 12px',
      },
    },
  },
  medusa: {
    name: 'Medusa',
    preview: 'Modern & Sleek',
    isPro: true,
    styles: {
      fontFamily: 'Space Grotesk, sans-serif',
      fontSize: '2rem',
      lineHeight: 1.3,
      textAlign: 'center',
      color: '#FFFFFF',
      fontWeight: 500,
      letterSpacing: '0.02em',
      highlightStyle: {
        backgroundColor: 'rgba(34, 197, 94, 0.95)',
        color: '#FFFFFF',
        scale: 1.06,
        fontWeight: 600,
        borderRadius: '8px',
        padding: '4px 12px',
      },
    },
  },
  minimal: {
    name: 'Minimal',
    preview: 'Simple & Clean',
    styles: {
      fontFamily: 'Inter, sans-serif',
      fontSize: '1.5rem',
      lineHeight: 1.2,
      textAlign: 'center',
      color: '#FFFFFF',
      backgroundColor: 'rgba(0,0,0,0.6)',
      padding: '4px 22px',
      highlightStyle: {
        backgroundColor: 'rgba(0,0,0,0.8)',
        color: '#FFFFFF',
        scale: 1.02,
        fontWeight: 500,
        padding: '2px 8px',
        borderRadius: '4px',
      },
    },
  },
  neon: {
    name: 'Neon',
    preview: 'Vibrant & Electric',
    isPro: true,
    styles: {
      fontFamily: 'Outfit, sans-serif',
      fontSize: '2.2rem',
      lineHeight: 1.3,
      textAlign: 'center',
      color: '#FFFFFF',
      textShadow:
        '0 0 10px rgba(255,255,255,0.9), 0 0 20px rgba(255,255,255,0.5), 0 0 30px rgba(255,255,255,0.3)',
      highlightStyle: {
        backgroundColor: 'rgba(139, 92, 246, 0.25)',
        color: '#F0ABFC',
        scale: 1.07,
        fontWeight: 600,
        textShadow:
          '0 0 10px rgba(240,171,252,0.9), 0 0 20px rgba(240,171,252,0.5), 0 0 30px rgba(240,171,252,0.3)',
        borderRadius: '8px',
        padding: '4px 16px',
      },
    },
  },
  handwritten: {
    name: 'Handwritten',
    preview: 'Casual & Friendly',
    styles: {
      fontFamily: 'Caveat, cursive',
      fontSize: '2.2rem',
      lineHeight: 1.4,
      textAlign: 'center',
      color: '#FFFFFF',
      textShadow: '1px 1px 2px rgba(0,0,0,0.6)',
      fontWeight: 'normal',
      padding: '8px',
      highlightStyle: {
        backgroundColor: 'rgba(34, 197, 94, 0.9)',
        color: '#FFFFFF',
        scale: 1.08,
        fontWeight: 600,
        textShadow: '1px 1px 2px rgba(0,0,0,0.4)',
        borderRadius: '8px',
      },
    },
  },
  retro: {
    name: 'Retro',
    preview: 'Vintage & Bold',
    isPro: true,
    styles: {
      fontFamily: 'Rubik Mono One, sans-serif',
      fontSize: '1.8rem',
      lineHeight: 1.2,
      textAlign: 'center',
      color: '#FFD700',
      textShadow: '3px 3px 0 #FF4500',
      letterSpacing: '0.02em',
      highlightStyle: {
        backgroundColor: '#FF4500',
        color: '#FFD700',
        scale: 1.05,
        fontWeight: 400,
        textShadow: '2px 2px 0 #000000',
        borderRadius: '0px',
        padding: '2px 8px',
      },
    },
  },
  cinematic: {
    name: 'Cinematic',
    preview: 'Dramatic & Bold',
    isPro: true,
    styles: {
      fontFamily: 'Playfair Display, serif',
      fontSize: '2.4rem',
      lineHeight: 1.3,
      textAlign: 'center',
      color: '#FFFFFF',
      fontWeight: 700,
      letterSpacing: '0.03em',
      textShadow: '2px 2px 8px rgba(0,0,0,0.6)',
      highlightStyle: {
        backgroundColor: 'rgba(245, 158, 11, 0.9)',
        color: '#FFFFFF',
        scale: 1.06,
        fontWeight: 700,
        textShadow: '2px 2px 8px rgba(0,0,0,0.6)',
        borderRadius: '2px',
        padding: '4px 16px',
      },
    },
  },
  gradient: {
    name: 'Gradient',
    preview: 'Smooth & Modern',
    isPro: true,
    styles: {
      fontFamily: 'Plus Jakarta Sans, sans-serif',
      fontSize: '2.2rem',
      lineHeight: 1.3,
      textAlign: 'center',
      color: '#FFFFFF',
      fontWeight: 600,
      background:
        'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0))',
      backdropFilter: 'blur(8px)',
      padding: '12px 24px',
      borderRadius: '12px',
      textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
      highlightStyle: {
        backgroundColor: 'black',
        color: '#FFFFFF',
        scale: 1.05,
        fontWeight: 700,
        textShadow: '1px 1px 2px rgba(0,0,0,0.2)',
        borderRadius: '8px',
        padding: '4px 16px',
      },
    },
  },
  future: {
    name: 'Future',
    preview: 'Sci-fi & Techy',
    isPro: true,
    styles: {
      fontFamily: 'Space Grotesk, sans-serif',
      fontSize: '2.1rem',
      lineHeight: 1.3,
      textAlign: 'center',
      color: '#E2E8F0',
      fontWeight: 600,
      letterSpacing: '0.02em',
      textShadow: '0 0 15px rgba(148,163,184,0.5)',
      padding: '12px',
      highlightStyle: {
        backgroundColor: 'rgba(14,165,233,0.15)',
        color: '#38BDF8',
        scale: 1.06,
        fontWeight: 700,
        textShadow: '0 0 15px rgba(56,189,248,0.6)',
        borderRadius: '4px',
        padding: '4px 16px',
        border: '1px solid rgba(56,189,248,0.3)',
        backdropFilter: 'blur(4px)',
      },
    },
  },
}
