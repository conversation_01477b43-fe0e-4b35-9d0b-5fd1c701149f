import { execSync } from 'child_process'
import { copyFileSync, mkdirSync, readdirSync, rmSync } from 'fs'
import { dirname, join } from 'path'
import { fileURLToPath } from 'url'
import dotenv from 'dotenv'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 读取环境变量
dotenv.config({ path: join(__dirname, '.env') })

const version = process.env.VITE_RENDERER_VERSION
if (!version) {
  console.error('VITE_RENDERER_VERSION not found in .env file')
  process.exit(1)
}

console.log(`Building for version: ${version}`)

// 清理之前的构建产物
const distDir = join(__dirname, 'dist')
const releaseDir = join(__dirname, 'release')
const versionDir = join(releaseDir, version)

try {
  rmSync(distDir, { recursive: true, force: true })
  rmSync(versionDir, { recursive: true, force: true })
} catch (error) {
}

// 执行 vite 构建
console.log('Running vite build...')
execSync('npm run build', { stdio: 'inherit', cwd: __dirname })

// 创建版本目录
mkdirSync(versionDir, { recursive: true })

// 复制构建产物到版本目录
const distFiles = readdirSync(distDir)
for (const file of distFiles) {
  if (file.endsWith('.js')) {
    const srcPath = join(distDir, file)
    const destPath = join(versionDir, file)
    copyFileSync(srcPath, destPath)
    console.log(`Copied ${file} to ${version}/`)
  }
}

try {
  rmSync(distDir, { recursive: true, force: true })
} catch (error) {
}

console.log(`Build completed! Output: release/${version}/`)
