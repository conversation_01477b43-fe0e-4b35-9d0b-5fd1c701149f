import console from 'console'
import process from 'process'

console.log('🔍 环境变量测试开始...')

console.log('\n📋 检查 COS 配置:')
console.log(`COS_SECRET_ID: ${process.env.COS_SECRET_ID ? '已设置 (***' + process.env.COS_SECRET_ID.slice(-4) + ')' : '❌ 未设置'}`)
console.log(`COS_SECRET_KEY: ${process.env.COS_SECRET_KEY ? '已设置 (***' + process.env.COS_SECRET_KEY.slice(-4) + ')' : '❌ 未设置'}`)
console.log(`COS_REGION: ${process.env.COS_REGION || '❌ 未设置'}`)
console.log(`COS_BUCKET: ${process.env.COS_BUCKET || '❌ 未设置'}`)

console.log('\n📋 其他配置:')
console.log(`VITE_DISTRIBUTION_CHANNEL: ${process.env.VITE_DISTRIBUTION_CHANNEL || '未设置'}`)
console.log(`AUTO_UPLOAD: ${process.env.AUTO_UPLOAD || '未设置'}`)

const requiredVars = ['COS_SECRET_ID', 'COS_SECRET_KEY', 'COS_REGION', 'COS_BUCKET']
const missingVars = requiredVars.filter(varName => !process.env[varName])

if (missingVars.length === 0) {
  console.log('\n✅ 所有必需的环境变量都已设置')
} else {
  console.log(`\n❌ 缺少以下环境变量: ${missingVars.join(', ')}`)
}

console.log('\n🎉 环境变量测试完成')
