{"name": "@app/preload", "type": "module", "scripts": {"build": "vite build", "typecheck": "tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "exports": {".": {"types": "./src/index.ts", "default": "./dist/_virtual_browser.mjs"}, "./exposed.mjs": {"types": "./src/exposed.d.ts", "default": "./dist/exposed.mjs"}}, "devDependencies": {"@app/electron-versions": "*", "globals": "^16.0.0", "mlly": "1.7.4", "typescript": "5.8.3", "typescript-eslint": "^8.30.1", "vite": "6.3.5"}}