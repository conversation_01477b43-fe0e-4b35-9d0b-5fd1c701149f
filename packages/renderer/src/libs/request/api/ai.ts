import request from '@/libs/request/request'

export namespace AiModule {

  export type AiFunctions =
    | 'denoise'
    | 'recognition'
    | 'synthesis'

  export type DenoiseRequestParams = {
    oss_url: string
  }

  export type RecognitionRequestParams = {
    oss_url: string
  }

  export type TextToSpeechRequestParams = {
    text: string
    voice_id: number
    volume: number
    speed: number
    pitch: number
  }

  export type DenoiseResult = string

  export type RecognitionResult = {
    srt_name: string;
    text: string
  }

  export type TextToSpeechResult = {
    srt_name: string;
    video_name: string;
  }

  export type RequestParamsByFunction<TFunc extends AiFunctions> = TFunc extends 'denoise'
    ? DenoiseRequestParams
    : TFunc extends 'recognition'
      ? RecognitionRequestParams
      : TFunc extends 'synthesis'
        ? TextToSpeechRequestParams
        : never

  export type TaskResultByFunction<TFunc extends AiFunctions> = TFunc extends 'denoise'
    ? DenoiseResult
    : TFunc extends 'recognition'
      ? RecognitionResult
      : TFunc extends 'synthesis'
        ? TextToSpeechResult
        : never

  export type GenericTaskInfo<TFunc extends AiFunctions> = {
    result: {
      data: TaskResultByFunction<TFunc>
      /**
       * 该任务是什么功能
       */
      func: string;
    };
    /**
     * 状态：
     * PENDING: 任务正在执行
     * SUCCESS: 任务执行成功
     * FAILURE: 任务异常
     * REVOKED: 任务取消
     */
    status: 'PENDING' | 'SUCCESS' | 'FAILURE' | 'REVOKED';
    task_id: string;
  }

  export const endpoints = {
    submitTask<TFunc extends AiFunctions>(func: TFunc, params: RequestParamsByFunction<TFunc>) {
      return request.post<{ task_id: string }>(`/ai/api/${func}`, params)
    },
    queryTaskStatus<TFunc extends AiFunctions>(task_id: string) {
      return request.get<GenericTaskInfo<TFunc>>(`/ai/api/status/${task_id}`)
    },
  }
}
