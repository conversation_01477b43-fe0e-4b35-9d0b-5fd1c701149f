import React, { useCallback } from 'react'
import { StyledTextResource } from '@/types/resources'

import StyledTextSelector from '@/modules/video-editor/components/common/styled-text-selector'
import { useTextSettingContext } from './context'
import { buildTextOverlay } from '@/modules/video-editor/utils/text'

export const StyledTextPanel = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  const handleFontStyleSelect = useCallback(
    async (data: StyledTextResource.StyledText) => {
      if (!textOverlay) return

      return requestUpdateText(
        buildTextOverlay(data, { baseOverlay: textOverlay, isPreview: false }),
        true
      )
    },
    [textOverlay, requestUpdateText]
  )

  return (
    <div className="h-full flex flex-col">

      <div className="flex-1 overflow-hidden">
        <StyledTextSelector
          onItemClicked={handleFontStyleSelect}
          itemDraggable={false}
          className="h-full flex flex-wrap"
        />
      </div>
    </div>
  )
}
