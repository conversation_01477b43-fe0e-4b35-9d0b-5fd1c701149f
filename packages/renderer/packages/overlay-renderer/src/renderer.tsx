import React from 'react'
import { AbsoluteFill } from 'remotion'
import { RenderContext } from './render.context'
import { MainProps } from './types'
import { Layer } from './layer/layer'

/**
 * Main component that renders a canvas-like area with overlays and their outlines.
 * Handles selection of overlays and provides a container for editing them.
 *
 * @param props - Component props of type MainProps
 * @returns React component that displays overlays and their interactive outlines
 */
export const Renderer: React.FC<MainProps> = ({
  overlays, baseUrl, playerMetadata
}) => {
  if (!playerMetadata) {
    throw new Error('Unable to load player metadata')
  }

  return (
    <RenderContext.Provider
      value={{
        overlays,
        baseUrl,
        playerMetadata
      }}
    >
      <AbsoluteFill style={{ backgroundColor: '#000000' }}>
        {overlays.map(overlay => (
          <Layer
            key={overlay.id}
            overlay={overlay}
            baseUrl={baseUrl}
          />
        ))}
      </AbsoluteFill>
    </RenderContext.Provider>
  )
}

