# ClipNest 部署指南

## 📋 概述

ClipNest 使用完全自动化的构建、打包和更新发布流程。支持多平台（Windows/macOS/Linux）和多渠道（latest/beta/alpha）发布。

## 🔧 环境配置

### 1. 必需的环境变量

在 `.env` 文件中配置以下变量：

```env
# 腾讯云 COS 配置（必需）
COS_SECRET_ID=your_secret_id_here
COS_SECRET_KEY=your_secret_key_here
COS_REGION=ap-guangzhou
COS_BUCKET=dev-common-1304320301

# 发布渠道配置（可选，默认从版本号推断）
VITE_DISTRIBUTION_CHANNEL=beta

# 自动上传开关（可选）
AUTO_UPLOAD=true
```

### 2. 版本和渠道管理

**版本号格式决定发布渠道**：
- `1.0.0` → `latest` 渠道（正式版）
- `1.0.0-beta.1` → `beta` 渠道（测试版）
- `1.0.0-alpha.1` → `alpha` 渠道（内测版）
- `1.0.0-dev.1` → `dev` 渠道（开发版）

**环境变量优先级**：
`VITE_DISTRIBUTION_CHANNEL` > 版本号推断

## 🚀 核心命令

### 构建和发布

| 命令 | 说明 | 用途 |
|------|------|------|
| `npm run build` | 仅构建应用 | 开发测试 |
| `npm run package` | 构建 + 打包安装程序 | 生成可分发文件 |
| `npm run upload` | 上传构建产物到 COS | 发布更新 |
| `npm run test:env` | 测试环境变量配置 | 配置验证 |

### 一键发布流程

```bash
# 完整发布流程（推荐）
npm run package && npm run upload
```

## 📦 多平台构建

### 支持的平台和格式

| 平台 | 安装包格式 | 自定义安装路径 | 说明 |
|------|------------|----------------|------|
| **Windows** | `.exe` (NSIS) | ✅ 是 | 完整安装向导，支持路径选择 |
| **macOS** | `.dmg` | ✅ 是 | 拖拽安装，用户可选择位置 |
| **Linux** | `.deb`, `.AppImage` | 部分支持 | DEB 标准路径，AppImage 便携 |

### Windows 安装程序特性

- ✅ **自定义安装路径**：用户可选择安装目录
- ✅ **安装向导**：完整的 NSIS 安装界面
- ✅ **快捷方式**：自动创建桌面和开始菜单快捷方式
- ✅ **权限管理**：自动请求管理员权限（如需要）
- ✅ **卸载程序**：完整的卸载支持

## 🔄 自动更新机制

### 更新检查流程

1. **应用启动**：自动检查更新（后台进行）
2. **平台检测**：根据运行平台请求对应的更新清单
3. **版本比较**：比较本地版本与服务器版本
4. **下载更新**：如有新版本，下载对应平台的安装包
5. **用户确认**：提示用户是否安装更新

### 更新清单文件

不同平台使用不同的更新清单文件：
- **Windows**: `{channel}.yml` (如 `beta.yml`)
- **macOS**: `{channel}-mac.yml` (如 `beta-mac.yml`)
- **Linux**: `{channel}-linux.yml` (如 `beta-linux.yml`)

## 🗂️ COS 存储结构

### 文件组织方式

```
clipnest-update/
├── beta/                     # 测试版渠道（当前使用）
│   └── latest/
│       ├── beta.yml          # Windows 更新清单
│       ├── beta-mac.yml      # macOS 更新清单
│       ├── beta-linux.yml    # Linux 更新清单
│       ├── clipnest-1.0.0-beta.1-win-x64.exe
│       ├── clipnest-1.0.0-beta.1-win-x64.exe.blockmap
│       ├── clipnest-1.0.0-beta.1-mac.dmg
│       ├── clipnest-1.0.0-beta.1-mac.dmg.blockmap
│       ├── clipnest-1.0.0-beta.1-linux.deb
│       └── clipnest-1.0.0-beta.1-linux.deb.blockmap
├── latest/                   # 正式版渠道
│   └── latest/
│       ├── latest.yml
│       ├── latest-mac.yml
│       ├── latest-linux.yml
│       └── ...
├── alpha/                    # 内测版渠道
└── dev/                      # 开发版渠道
```

### 访问 URL 示例

**更新检查 URL**：
- Windows: `https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/clipnest-update/beta/latest/beta.yml`
- macOS: `https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/clipnest-update/beta/latest/beta-mac.yml`
- Linux: `https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/clipnest-update/beta/latest/beta-linux.yml`

## 🔧 发布流程

### 标准发布流程

1. **更新版本号**：
   ```bash
   # 修改 package.json 中的 version 字段
   # 例如：1.0.0-beta.1 → 1.0.0-beta.2
   ```

2. **一键构建和发布**：
   ```bash
   # 完整流程：构建 → 打包 → 上传
   npm run package && npm run upload
   ```

3. **验证发布**：
   - 检查 COS 中的文件是否正确上传
   - 测试应用的自动更新功能

### 分步执行（可选）

如果需要分步控制，可以单独执行：

```bash
# 1. 仅构建应用
npm run build

# 2. 仅打包安装程序
npm run package

# 3. 仅上传文件
npm run upload
```

### 渠道管理

**当前渠道检查**：
```bash
# 查看当前配置的渠道
npm run test:env
```

**切换发布渠道**：
```bash
# 方法1：修改 .env 文件中的 VITE_DISTRIBUTION_CHANNEL
# 方法2：修改 package.json 中的版本号格式

# 示例：切换到正式版
# package.json: "version": "1.0.0"
# .env: VITE_DISTRIBUTION_CHANNEL=latest

# 示例：切换到测试版
# package.json: "version": "1.0.0-beta.1"
# .env: VITE_DISTRIBUTION_CHANNEL=beta
```

## 🚨 故障排除

### 常见问题

1. **构建失败**：
   - 检查 Node.js 版本是否兼容
   - 清理依赖：`rm -rf node_modules && npm install`
   - 检查磁盘空间是否充足

2. **上传失败**：
   - 验证 COS 配置：`npm run test:env`
   - 检查网络连接
   - 确认存储桶权限

3. **自动更新不工作**：
   - 检查更新清单文件是否存在
   - 验证版本号格式
   - 确认渠道配置一致性

### 调试命令

```bash
# 测试环境配置
npm run test:env

# 检查构建产物
ls -la dist/

# 验证上传的文件
# 访问 COS 控制台或使用 API 检查文件
```

## ✅ 功能特性

### 已实现功能

- ✅ **多平台构建**：Windows/macOS/Linux
- ✅ **自动更新**：完整的更新检查和下载机制
- ✅ **多渠道发布**：latest/beta/alpha/dev
- ✅ **自定义安装**：Windows 支持用户选择安装路径
- ✅ **文件完整性**：SHA512 校验和 blockmap 支持
- ✅ **自动上传**：一键构建和发布
- ✅ **版本管理**：基于语义化版本的渠道推断

### 技术栈

- **构建工具**：electron-builder
- **更新机制**：electron-updater
- **存储服务**：腾讯云 COS
- **安装程序**：NSIS (Windows), DMG (macOS), DEB/AppImage (Linux)

## 📋 快速参考

### 常用命令速查

```bash
# 🚀 一键发布（最常用）
npm run package && npm run upload

# 🔧 环境检查
npm run test:env

# 🏗️ 仅构建
npm run build

# 📦 仅打包
npm run package

# ⬆️ 仅上传
npm run upload

# 🧪 开发调试
npm start
```

### 版本号规范

| 格式 | 渠道 | 用途 | 示例 |
|------|------|------|------|
| `x.y.z` | latest | 正式发布 | `1.0.0` |
| `x.y.z-beta.n` | beta | 测试版本 | `1.0.0-beta.1` |
| `x.y.z-alpha.n` | alpha | 内测版本 | `1.0.0-alpha.1` |
| `x.y.z-dev.n` | dev | 开发版本 | `1.0.0-dev.1` |

### 文件检查清单

发布前确认以下文件：
- ✅ `package.json` - 版本号正确
- ✅ `.env` - 环境变量配置
- ✅ `electron-builder.mjs` - 构建配置
- ✅ `buildResources/` - 图标和资源文件

---

## 📞 支持

如有问题，请检查：
1. 环境变量配置是否正确
2. 网络连接是否正常
3. COS 存储桶权限是否充足
4. 版本号格式是否符合规范

**调试步骤**：
1. 运行 `npm run test:env` 检查配置
2. 检查 `dist/` 目录是否有构建产物
3. 查看 COS 控制台确认文件上传
4. 测试应用的自动更新功能
