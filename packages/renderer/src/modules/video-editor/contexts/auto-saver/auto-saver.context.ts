import { createContext, useContext } from 'react'

export interface AutoSaverContextType {
  /**
   * 是否正在保存
   */
  isSaving: boolean

  /**
   * 最后保存时间戳
   */
  lastSaveTime: number | null

  /**
   * 手动保存项目
   */
  saveProject: () => Promise<void>
}

export const AutoSaverContext = createContext<AutoSaverContextType | undefined>(
  undefined,
)

export const useAutoSaver = () => {
  const context = useContext(AutoSaverContext)
  if (!context) {
    throw new Error(
      'useAutoSaver must be used within an AutoSaverProvider',
    )
  }
  return context
}
