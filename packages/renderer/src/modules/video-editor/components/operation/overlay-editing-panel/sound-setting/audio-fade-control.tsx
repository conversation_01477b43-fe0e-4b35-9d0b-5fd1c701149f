import React from 'react'
import { SoundOverlay } from '@clipnest/remotion-shared/types'
import { FadeControls } from '@/modules/video-editor/components/common/fade-controls'

export const AudioFadeControls: React.FC<{
  overlay: SoundOverlay
  onOverlayChange: (updates: Partial<SoundOverlay>, commit?: boolean) => void
  className?: string
}> = ({ overlay, onOverlayChange, className }) => {
  return (
    <FadeControls
      overlay={overlay}
      onOverlayChange={onOverlayChange}
      maxFadeInDuration={5}
      maxFadeOutDuration={5}
      className={className}
    />
  )
}
