
import React from 'react'
import { ChromePicker, ColorPickerProps } from 'react-color'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

const chromePickerStyles = `
  .chrome-picker input {
    background-color: hsl(var(--input)) !important;
    color: hsl(var(--foreground)) !important;
    border: none !important;
    border-radius: 4px !important;
    box-shadow: none !important;
  }
  .chrome-picker input:focus-visible, .chrome-picker input:active {
    box-shadow: none !important;
    border: 1px solid red !important;
  }
  .chrome-picker label {
    color: hsl(var(--muted-foreground)) !important;
  }
`

export const ColorPicker: React.FC<ColorPickerProps<any>> = props => {
  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: chromePickerStyles }} />

      <Popover>
        <PopoverTrigger asChild>
          <div
            className="h-8 w-8 rounded-md border cursor-pointer"
            style={{
              backgroundColor: props.color as any
            }}
          />
        </PopoverTrigger>
        <PopoverContent
          className="w-[330px] bg-background border border-border"
          side="right"
        >
          <ChromePicker
            {...props}
            styles={{
              default: {
                picker: {
                  background: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '8px',
                  boxShadow: 'none',
                },
                saturation: {
                  background: 'hsl(var(--muted))',
                },
                hue: {
                  background: 'hsl(var(--muted))',
                },
                alpha: {
                  background: 'hsl(var(--muted))',
                },
              },
            }}
          />
        </PopoverContent>
      </Popover>
    </>
  )
}
