import React, { useCallback, useMemo } from 'react'
import { FormNumberInput, FormSlider, SectionTitle } from '@/modules/video-editor/components/common/form-components'
import { MediaPaddingControls } from '@/modules/video-editor/components/common/media-padding-controls'
import { FPS } from '../../../../../constants'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { MediaControls } from '@/modules/video-editor/components/common/media-controls'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import { VideoFadeControls } from '../video-fade-control'
import _ from 'lodash'

const TrimSetting: React.FC = () => {
  const frameToSecond = (frame = 0) =>  _.round((frame ?? 0) / FPS, 1)

  const { localOverlay: videoOverlay, updateEditingOverlay } = useOverlayEditing<VideoOverlay>()

  const { trimStartMax, trimEndMax } = useMemo(() => {
    const minDuration = FPS * 0.5
    return {
      trimStartMax: videoOverlay.originalDurationInFrames - minDuration - (videoOverlay.trimEndFrames ?? 0),
      trimEndMax: videoOverlay.originalDurationInFrames - minDuration - (videoOverlay.trimStartFrames ?? 0)
    }
  }, [videoOverlay])

  const trimVideo = useCallback((props: { start?: number, end?: number }) => {
    return updateEditingOverlay(overlay => {
      const trimStartFrames = (props.start !== undefined ? props.start * FPS : undefined) ?? overlay.trimStartFrames ?? 0
      const trimEndFrames = (props.end !== undefined ? props.end * FPS : undefined) ?? overlay.trimEndFrames ?? 0

      return {
        ...overlay,
        trimStartFrames,
        trimEndFrames,
        durationInFrames: Math.max(0, overlay.originalDurationInFrames - trimEndFrames - trimStartFrames)
      }
    })
  }, [updateEditingOverlay])

  return (
    <div className="overlay-setting-card">
      <SectionTitle
        title="视频裁剪"
        onReset={() => {
          updateEditingOverlay(o => ({
            ...o,
            trimStartFrames: 0,
            trimEndFrames: 0,
            durationInFrames: o.originalDurationInFrames * (o.speed ?? 1)
          }), true)
        }}
      />
      <div className="grid grid-cols-2 gap-3">
        <FormNumberInput
          label="去片头"
          suffix="秒"
          value={frameToSecond(videoOverlay.trimStartFrames)}
          onChange={value => trimVideo({ start: value })}
          onBlur={() => updateEditingOverlay({}, true)}
          min={0}
          max={frameToSecond(trimStartMax)}
          step={0.1}
          decimalPlaces={1}
          formatDisplay={true}
        />
        <FormNumberInput
          label="去片尾"
          suffix="秒"
          value={frameToSecond(videoOverlay.trimEndFrames)}
          onChange={value => trimVideo({ end: value })}
          onBlur={() => updateEditingOverlay({}, true)}
          min={0}
          max={frameToSecond(trimEndMax)}
          step={0.1}
          decimalPlaces={1}
          formatDisplay={true}
        />
      </div>

      {/* 显示裁剪后的视频时长 */}
      {Boolean(videoOverlay.trimStartFrames || videoOverlay.trimEndFrames) && (
        <div className="flex justify-between text-xs text-muted-foreground  p-2 rounded-md">
          <span>裁剪后时长(该时长不受变速影响):</span>
          <span className="font-medium">
            {frameToSecond(videoOverlay.originalDurationInFrames - (videoOverlay.trimStartFrames ?? 0) - (videoOverlay.trimEndFrames ?? 0))}秒
          </span>
        </div>
      )}
    </div>
  )
}

export function BasicSettings() {
  const { localOverlay: videoOverlay, updateEditingOverlay } = useOverlayEditing<VideoOverlay>()

  return (
    <div className="space-y-2 w-full">
      {/*基本布局*/}
      <div className="overlay-setting-card">
        <SectionTitle title="基本布局" />
        <div className="grid grid-cols-2 gap-3 mb-3">
          <FormNumberInput
            label="X"
            value={videoOverlay.left}
            onChange={val => updateEditingOverlay({ left: val }, true)}
            decimalPlaces={2}
          />
          <FormNumberInput
            label="Y"
            value={videoOverlay.top}
            onChange={val => updateEditingOverlay({ top: val }, true)}
            decimalPlaces={2}
          />
        </div>

        <FormSlider
          min={-180}
          max={180}
          step={1}
          value={videoOverlay.rotation}
          label="旋转(°)"
          onChange={(val, commit) => updateEditingOverlay({ rotation: val }, commit)}
        />
        <MediaPaddingControls />
      </div>

      {/* 基础 */}
      <div className="overlay-setting-card">
        <SectionTitle title="基础" />
        <FormSlider
          max={1}
          min={0}
          step={0.1}
          value={videoOverlay?.styles?.opacity ?? 1}
          onChange={(val, commit) => updateEditingOverlay({ styles: { opacity: val } }, commit)}
          label="不透明度"
        />
      </div>

      {/* 音量 */}
      <div className="overlay-setting-card">
        <SectionTitle title="音量" />
        <FormSlider
          value={videoOverlay?.styles?.volume ?? 1}
          onChange={(val, commit) => updateEditingOverlay({ styles: { volume: val } }, commit)}
          max={1}
          min={0}
          step={0.1}
        />
        <div className="gap-6 flex items-center mt-2">
          <div className="flex items-center gap-2">
            <Checkbox id="gain" />
            <Label htmlFor="gain">音频增益</Label>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox id="reduction" />
            <Label htmlFor="reduction">音频降噪</Label>
          </div>
        </div>
      </div>

      {/* 淡入淡出控制 */}
      <div className="overlay-setting-card">
        <VideoFadeControls
          overlay={videoOverlay}
          onOverlayChange={updateEditingOverlay}
        />
      </div>

      {/* 视频裁剪 */}
      <TrimSetting />

      {/* 变速和时长控制 */}
      <MediaControls
        overlay={videoOverlay}
        onOverlayPropertyChange={updateEditingOverlay}
        minSpeed={0.25}
        maxSpeed={4}
        speedStep={0.25}
        minDuration={0.1}
        maxDuration={videoOverlay.originalDurationInFrames / FPS}
        durationStep={0.1}
      />
    </div>
  )
}
